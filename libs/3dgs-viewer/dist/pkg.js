var Dr = Object.defineProperty;
var kr = (n, e, t) => e in n ? Dr(n, e, { enumerable: !0, configurable: !0, writable: !0, value: t }) : n[e] = t;
var dn = (n, e, t) => kr(n, typeof e != "symbol" ? e + "" : e, t);
import { Matrix4 as $e, Vector3 as k, ShaderChunk as Tr, Vector4 as Xe, InstancedBufferGeometry as zs, BufferAttribute as xt, InstancedBufferAttribute as Rr, DynamicDrawUsage as Lr, ShaderMaterial as aa, DoubleSide as et, NormalBlending as Fr, DataTexture as dt, RGBAIntegerFormat as ut, UnsignedIntType as ht, Mesh as Ie, Vector2 as ye, Loader as Pr, FileLoader as _r, Color as yi, SRGBColorSpace as Ur, Group as on, BufferGeometry as Yn, Float32BufferAttribute as Ze, LineBasicMaterial as Tn, Material as Wi, PointsMaterial as un, MeshPhongMaterial as Nr, LineSegments as Vs, Points as fs, WebGLRenderer as ra, PerspectiveCamera as ca, Object3D as is, CylinderGeometry as Hi, MeshBasicMaterial as at, PlaneGeometry as Wr, Quaternion as rt, Controls as Hr, MOUSE as qe, TOUCH as st, Spherical as Yi, Ray as Yr, Plane as Gr, MathUtils as it, InstancedInterleavedBuffer as Os, InterleavedBufferAttribute as Qt, WireframeGeometry as zr, Box3 as Ii, Sphere as la, ShaderLib as Rn, UniformsUtils as Aa, UniformsLib as Ln, Line3 as Vr, CircleGeometry as gt, AudioLoader as Or, Audio as Xr, AudioListener as Jr, Raycaster as Kr, CatmullRomCurve3 as Gi, Scene as da, AmbientLight as ua, FogExp2 as zi, DirectionalLight as Zr, EventDispatcher as jr, Clock as qr } from "three";
import * as Vi from "@gotoeasy/three-tile";
let w = 1;
const Rt = w++, ha = w++, je = w++, ga = w++, Xs = w++, Js = w++, fa = w++, ps = w++, pa = w++, _t = w++, $ = w++, H = w++, ma = w++, Ks = w++, Ve = w++, Lt = w++, Ue = w++, an = w++, Ca = w++, ya = w++, Ia = w++, wa = w++, Ea = w++, ba = w++, wi = w++, $r = w++, ec = w++, ms = w++, me = w++;
w++;
const ce = w++, pt = w++, Dt = w++, Oi = w++, Xi = w++, Ji = w++, Sa = w++, tc = w++, Ki = w++, hn = w++, gn = w++, Zi = w++, ji = w++, qi = w++, ft = w++, $i = w++, jt = w++, va = w++, Ma = w++, xa = w++, bt = w++, Nt = w++, Fn = w++, Qa = w++, Zs = w++, nc = w++, Gn = w++, Cs = w++, ys = w++, zn = w++, Vn = w++, os = w++, On = w++, Ba = w++, Ei = w++, F = w++, It = w++, qt = w++, V = w++, as = w++, bi = w++, sc = w++, Ee = w++, Da = w++, Qe = w++, ka = w++, Ta = w++, Ra = w++, La = w++, Pn = w++, Fa = w++, mt = w++, Si = w++, Xn = w++, Jt = w++, Pa = w++, _a = w++, Ua = w++, js = w++;
w++;
const rs = w++, Is = w++, ws = w++, Es = w++, qs = w++, Jn = w++, $s = w++, Kn = w++, ke = w++, vi = w++;
w++;
const Na = w++, Wa = w++, fn = w++, bs = w++, ei = w++, ic = w++, _n = w++, Zn = w++, Mi = w++, kt = w++, Ft = w++, jn = w++, pn = w++, Ha = w++, ne = w++, j = w++, oc = w++, eo = w++, qn = w++, $n = w++, ac = w++, xi = w++, Qi = w++, ct = w++, Kt = w++, Ut = w++, rc = w++, Ya = w++, cc = w++;
w++;
w++;
w++;
w++;
w++;
const lt = w++, $t = w++, Un = w++, ti = w++, Ga = w++, za = w++, ni = w++, tt = w++, Vt = w++, Va = w++, Bi = w++, to = w++, lc = w++, Ac = w++, rn = w++, cn = w++, ln = w++, cs = w++, ls = w++, ot = w++, Oa = w++, no = w++, es = w++, ve = w++, Xa = w++, si = w++, Ja = w++, Ot = w++, Ka = w++, Za = w++, Di = w++, ja = w++, qa = w++, so = w++, $a = w++, ki = w++, dc = w++, uc = w++, er = w++, ii = w++, hc = w++, tr = w++, ts = w++, Nn = w++, ns = w++, Ss = w++, vs = w++, Ms = w++, oi = w++, xs = w++, Ti = w++, io = w++, nr = w++;
w++;
w++;
w++;
w++;
const gc = w++, sr = w++, ir = w++;
w++;
const or = w++, ar = w++, de = w++, ai = w++, ri = w++, ci = w++, fc = w++, rr = w++, pc = w++, mc = w++, Cc = w++, yc = w++, Ic = w++, cr = w++, lr = w++;
w++;
w++;
const wc = w++, li = w++, Ai = w++, Qs = w++, oo = w++, Ec = w++, bc = w++, Sc = w++, Ar = w++, vc = w++, mn = w++, Cn = w++, Wt = w++, Ht = w++, yn = w++, In = w++, wn = w++, En = w++, bn = w++, Sn = w++;
class As {
  constructor() {
    this.map = /* @__PURE__ */ new Map();
  }
  on(e, t = null, s = !1) {
    if (!e)
      return console.error("Invalid event key", e), null;
    if (!t) return this.map.get(e);
    if (s) {
      let i = this.map.get(e);
      i ? typeof i == "function" ? console.error("Invalid event type", "multiFn=true", e) : i.push(t) : (i = [], this.map.set(e, i), i.push(t));
    } else {
      let i = this.map.get(e);
      i ? typeof i == "function" ? console.warn("Replace event", e) : console.error("Invalid event type", "multiFn=false", e) : this.map.set(e, t);
    }
    return this.map.get(e);
  }
  fire(e, ...t) {
    const s = this.map.get(e);
    if (!s) {
      this.map.size && [...t];
      return;
    }
    if (typeof s == "function")
      return s(...t);
    let i = [];
    return s.forEach((a) => i.push(a(...t))), i;
  }
  tryFire(e, ...t) {
    return this.map.get(e) ? this.fire(e, ...t) : void 0;
  }
  off(e) {
    this.map.delete(e);
  }
  clear() {
    this.map.clear();
  }
}
class Mc {
}
class xc {
  constructor(e, t = {}) {
    this.fileSize = 0, this.downloadSize = 0, this.status = 0, this.splatData = null, this.watermarkData = null, this.dataSplatCount = 0, this.watermarkCount = 0, this.sh12Data = [], this.sh3Data = [], this.sh12Count = 0, this.sh3Count = 0, this.rowLength = 0, this.modelSplatCount = -1, this.downloadSplatCount = 0, this.renderSplatCount = 0, this.header = null, this.CompressionRatio = "", this.dataShDegree = 0, this.minX = 1 / 0, this.maxX = -1 / 0, this.minY = 1 / 0, this.maxY = -1 / 0, this.minZ = 1 / 0, this.maxZ = -1 / 0, this.topY = 0, this.currentRadius = 0, this.maxRadius = 0, this.textWatermarkVersion = 0, this.lastTextWatermarkVersion = 0, this.fetchLimit = 0, this.opts = { ...e };
    const s = this;
    s.meta = t, t.autoCut && (s.map = /* @__PURE__ */ new Map()), s.metaMatrix = t.transform ? new $e().fromArray(t.transform) : null, e.format || (e.url?.endsWith(".spx") ? s.opts.format = "spx" : e.url?.endsWith(".splat") ? s.opts.format = "splat" : e.url?.endsWith(".ply") ? s.opts.format = "ply" : e.url?.endsWith(".spz") ? s.opts.format = "spz" : e.url?.endsWith(".sog") ? s.opts.format = "sog" : console.error("unknown format!")), s.abortController = new AbortController();
  }
}
var N = /* @__PURE__ */ ((n) => (n[n.FetchReady = 0] = "FetchReady", n[n.Fetching = 1] = "Fetching", n[n.FetchDone = 2] = "FetchDone", n[n.FetchAborted = 3] = "FetchAborted", n[n.FetchFailed = 4] = "FetchFailed", n[n.Invalid = 5] = "Invalid", n))(N || {});
const ds = "v2.1.0-dev", Ce = !navigator.userAgent.includes("cloudphone") && navigator.userAgent.includes("Mobi"), Qc = "QWERTYUIOPLKJHGFDSAZXCVBNM1234567890qwertyuioplkjhgfdsazxcvbnm`~!@#$%^&*()-_=+\\|]}[{'\";::,<.>//? 	", Bc = Ce ? 600 : 300, Dc = Ce ? 2e3 : 300, St = 128, Wn = 32, X = 32, kc = 20, Tc = 16, dr = 64 * 1024, Rc = 1024 * 1e4, Lc = 10240 * 1e4, Ye = 0.28209479177387814, Fc = 0, Pc = 20, _c = 190, Uc = 10190, en = 1, Oe = 2, wt = 3, Nc = 4, Wc = 3141592653;
var Be = /* @__PURE__ */ ((n) => (n[n.L1 = 1] = "L1", n[n.L2 = 2] = "L2", n[n.L3 = 3] = "L3", n[n.L4 = 4] = "L4", n[n.Default5 = 5] = "Default5", n[n.L6 = 6] = "L6", n[n.L7 = 7] = "L7", n[n.L8 = 8] = "L8", n[n.L9 = 9] = "L9", n))(Be || {}), tn = /* @__PURE__ */ ((n) => (n[n.Default1 = 1] = "Default1", n[n.ZdepthFrontNearest2010 = 2010] = "ZdepthFrontNearest2010", n[n.ZdepthFront2011 = 2011] = "ZdepthFront2011", n[n.ZdepthFrontNearFar2012 = 2012] = "ZdepthFrontNearFar2012", n[n.ZdepthFullNearFar2112 = 2112] = "ZdepthFullNearFar2112", n))(tn || {});
function Hc(n) {
  if (Object.prototype.hasOwnProperty.call(n, "__esModule")) return n;
  var e = n.default;
  if (typeof e == "function") {
    var t = function s() {
      return this instanceof s ? Reflect.construct(e, arguments, this.constructor) : e.apply(this, arguments);
    };
    t.prototype = e.prototype;
  } else t = {};
  return Object.defineProperty(t, "__esModule", { value: !0 }), Object.keys(n).forEach(function(s) {
    var i = Object.getOwnPropertyDescriptor(n, s);
    Object.defineProperty(t, s, i.get ? i : {
      enumerable: !0,
      get: function() {
        return n[s];
      }
    });
  }), t;
}
var Hn = { exports: {} };
const Yc = {}, Gc = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Yc
}, Symbol.toStringTag, { value: "Module" })), zc = /* @__PURE__ */ Hc(Gc);
/*!
 * Based on xzwasm (c) Steve Sanderson. License: MIT - https://github.com/SteveSanderson/xzwasm
 * Contains xz-embedded by Lasse Collin and Igor Pavlov. License: Public domain - https://tukaani.org/xz/embedded.html
 * and walloc (c) 2020 Igalia, S.L. License: MIT - https://github.com/wingo/walloc
 */
var Vc = Hn.exports, ao;
function Oc() {
  return ao || (ao = 1, (function(n, e) {
    (function(s, i) {
      n.exports = i(zc);
    })(Vc, (t) => (
      /******/
      (() => {
        var s = [
          ,
          /* 1 */
          /***/
          ((r) => {
            r.exports = "data:application/wasm;base64,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";
          }),
          /* 2 */
          /***/
          ((r) => {
            r.exports = t;
          })
          /******/
        ], i = {};
        function a(r) {
          var l = i[r];
          if (l !== void 0)
            return l.exports;
          var A = i[r] = {
            /******/
            // no module.id needed
            /******/
            // no module.loaded needed
            /******/
            exports: {}
            /******/
          };
          return s[r](A, A.exports, a), A.exports;
        }
        a.d = (r, l) => {
          for (var A in l)
            a.o(l, A) && !a.o(r, A) && Object.defineProperty(r, A, { enumerable: !0, get: l[A] });
        }, a.o = (r, l) => Object.prototype.hasOwnProperty.call(r, l), a.r = (r) => {
          typeof Symbol < "u" && Symbol.toStringTag && Object.defineProperty(r, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(r, "__esModule", { value: !0 });
        };
        var o = {};
        return (() => {
          a.r(o), a.d(o, {
            /* harmony export */
            XzReadableStream: () => (
              /* binding */
              p
            )
            /* harmony export */
          });
          var r = a(1);
          const l = globalThis.ReadableStream || a(2).ReadableStream, A = 0, c = 1;
          class d {
            constructor(g) {
              this.exports = g.exports, this.memory = this.exports.memory, this.ptr = this.exports.create_context(), this._refresh(), this.bufSize = this.mem32[0], this.inStart = this.mem32[1] - this.ptr, this.inEnd = this.inStart + this.bufSize, this.outStart = this.mem32[4] - this.ptr;
            }
            supplyInput(g) {
              this._refresh(), this.mem8.subarray(this.inStart, this.inEnd).set(g, 0), this.exports.supply_input(this.ptr, g.byteLength), this._refresh();
            }
            getNextOutput() {
              const g = this.exports.get_next_output(this.ptr);
              if (this._refresh(), g !== A && g !== c)
                throw new Error(`get_next_output failed with error code ${g}`);
              return { outChunk: this.mem8.slice(this.outStart, this.outStart + /* outPos */
              this.mem32[5]), finished: g === c };
            }
            needsMoreInput() {
              return (
                /* inPos */
                this.mem32[2] === /* inSize */
                this.mem32[3]
              );
            }
            outputBufferIsFull() {
              return (
                /* outPos */
                this.mem32[5] === this.bufSize
              );
            }
            resetOutputBuffer() {
              this.outPos = this.mem32[5] = 0;
            }
            dispose() {
              this.exports.destroy_context(this.ptr), this.exports = null;
            }
            _refresh() {
              this.memory.buffer !== this.mem8?.buffer && (this.mem8 = new Uint8Array(this.memory.buffer, this.ptr), this.mem32 = new Uint32Array(this.memory.buffer, this.ptr));
            }
          }
          class h {
            constructor() {
              this.locked = !1, this.waitQueue = [];
            }
            async acquire() {
              if (!this.locked) {
                this.locked = !0;
                return;
              }
              return new Promise((g) => {
                this.waitQueue.push(g);
              });
            }
            release() {
              this.waitQueue.length > 0 ? this.waitQueue.shift()() : this.locked = !1;
            }
          }
          const m = class m extends l {
            static async _getModuleInstance() {
              const g = r.replace("data:application/wasm;base64,", ""), f = Uint8Array.from(atob(g), (x) => x.charCodeAt(0)).buffer, u = {}, v = await WebAssembly.instantiate(f, u);
              m._moduleInstance = v.instance;
            }
            constructor(g) {
              let f, u = null;
              const v = g.getReader();
              super({
                async start(x) {
                  await m._contextMutex.acquire();
                  try {
                    m._moduleInstance || await (m._moduleInstancePromise || (m._moduleInstancePromise = m._getModuleInstance())), f = new d(m._moduleInstance);
                  } catch (R) {
                    throw m._contextMutex.release(), R;
                  }
                },
                async pull(x) {
                  try {
                    if (f.needsMoreInput()) {
                      if (u === null || u.byteLength === 0) {
                        const { done: D, value: S } = await v.read();
                        D || (u = S);
                      }
                      const M = Math.min(f.bufSize, u.byteLength);
                      f.supplyInput(u.subarray(0, M)), u = u.subarray(M);
                    }
                    const R = f.getNextOutput();
                    x.enqueue(R.outChunk), f.resetOutputBuffer(), R.finished && (f.dispose(), m._contextMutex.release(), x.close());
                  } catch (R) {
                    throw f && f.dispose(), m._contextMutex.release(), R;
                  }
                },
                cancel() {
                  try {
                    return f && f.dispose(), v.cancel();
                  } finally {
                    m._contextMutex.release();
                  }
                }
              });
            }
          };
          dn(m, "_moduleInstancePromise"), dn(m, "_moduleInstance"), dn(m, "_contextMutex", new h());
          let p = m;
        })(), o;
      })()
    ));
  })(Hn)), Hn.exports;
}
var Xc = Oc(), De = Uint8Array, Bt = Uint16Array, Jc = Int32Array, ur = new De([
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  1,
  1,
  1,
  1,
  2,
  2,
  2,
  2,
  3,
  3,
  3,
  3,
  4,
  4,
  4,
  4,
  5,
  5,
  5,
  5,
  0,
  /* unused */
  0,
  0,
  /* impossible */
  0
]), hr = new De([
  0,
  0,
  0,
  0,
  1,
  1,
  2,
  2,
  3,
  3,
  4,
  4,
  5,
  5,
  6,
  6,
  7,
  7,
  8,
  8,
  9,
  9,
  10,
  10,
  11,
  11,
  12,
  12,
  13,
  13,
  /* unused */
  0,
  0
]), Kc = new De([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]), gr = function(n, e) {
  for (var t = new Bt(31), s = 0; s < 31; ++s)
    t[s] = e += 1 << n[s - 1];
  for (var i = new Jc(t[30]), s = 1; s < 30; ++s)
    for (var a = t[s]; a < t[s + 1]; ++a)
      i[a] = a - t[s] << 5 | s;
  return { b: t, r: i };
}, fr = gr(ur, 2), pr = fr.b, Zc = fr.r;
pr[28] = 258, Zc[258] = 28;
var jc = gr(hr, 0), qc = jc.b, di = new Bt(32768);
for (var ae = 0; ae < 32768; ++ae) {
  var nt = (ae & 43690) >> 1 | (ae & 21845) << 1;
  nt = (nt & 52428) >> 2 | (nt & 13107) << 2, nt = (nt & 61680) >> 4 | (nt & 3855) << 4, di[ae] = ((nt & 65280) >> 8 | (nt & 255) << 8) >> 1;
}
var Zt = (function(n, e, t) {
  for (var s = n.length, i = 0, a = new Bt(e); i < s; ++i)
    n[i] && ++a[n[i] - 1];
  var o = new Bt(e);
  for (i = 1; i < e; ++i)
    o[i] = o[i - 1] + a[i - 1] << 1;
  var r;
  if (t) {
    r = new Bt(1 << e);
    var l = 15 - e;
    for (i = 0; i < s; ++i)
      if (n[i])
        for (var A = i << 4 | n[i], c = e - n[i], d = o[n[i] - 1]++ << c, h = d | (1 << c) - 1; d <= h; ++d)
          r[di[d] >> l] = A;
  } else
    for (r = new Bt(s), i = 0; i < s; ++i)
      n[i] && (r[i] = di[o[n[i] - 1]++] >> 15 - n[i]);
  return r;
}), An = new De(288);
for (var ae = 0; ae < 144; ++ae)
  An[ae] = 8;
for (var ae = 144; ae < 256; ++ae)
  An[ae] = 9;
for (var ae = 256; ae < 280; ++ae)
  An[ae] = 7;
for (var ae = 280; ae < 288; ++ae)
  An[ae] = 8;
var mr = new De(32);
for (var ae = 0; ae < 32; ++ae)
  mr[ae] = 5;
var $c = /* @__PURE__ */ Zt(An, 9, 1), el = /* @__PURE__ */ Zt(mr, 5, 1), Bs = function(n) {
  for (var e = n[0], t = 1; t < n.length; ++t)
    n[t] > e && (e = n[t]);
  return e;
}, Pe = function(n, e, t) {
  var s = e / 8 | 0;
  return (n[s] | n[s + 1] << 8) >> (e & 7) & t;
}, Ds = function(n, e) {
  var t = e / 8 | 0;
  return (n[t] | n[t + 1] << 8 | n[t + 2] << 16) >> (e & 7);
}, tl = function(n) {
  return (n + 7) / 8 | 0;
}, Ri = function(n, e, t) {
  return (e == null || e < 0) && (e = 0), (t == null || t > n.length) && (t = n.length), new De(n.subarray(e, t));
}, nl = [
  "unexpected EOF",
  "invalid block type",
  "invalid length/literal",
  "invalid distance",
  "stream finished",
  "no stream handler",
  ,
  "no callback",
  "invalid UTF-8 data",
  "extra field too long",
  "date not in range 1980-2099",
  "filename too long",
  "stream finishing",
  "invalid zip data"
  // determined by unknown compression method
], Re = function(n, e, t) {
  var s = new Error(e || nl[n]);
  if (s.code = n, Error.captureStackTrace && Error.captureStackTrace(s, Re), !t)
    throw s;
  return s;
}, sl = function(n, e, t, s) {
  var i = n.length, a = s ? s.length : 0;
  if (!i || e.f && !e.l)
    return t || new De(0);
  var o = !t, r = o || e.i != 2, l = e.i;
  o && (t = new De(i * 3));
  var A = function(re) {
    var Et = t.length;
    if (re > Et) {
      var At = new De(Math.max(Et * 2, re));
      At.set(t), t = At;
    }
  }, c = e.f || 0, d = e.p || 0, h = e.b || 0, p = e.l, m = e.d, y = e.m, g = e.n, f = i * 8;
  do {
    if (!p) {
      c = Pe(n, d, 1);
      var u = Pe(n, d + 1, 3);
      if (d += 3, u)
        if (u == 1)
          p = $c, m = el, y = 9, g = 5;
        else if (u == 2) {
          var M = Pe(n, d, 31) + 257, D = Pe(n, d + 10, 15) + 4, S = M + Pe(n, d + 5, 31) + 1;
          d += 14;
          for (var P = new De(S), G = new De(19), C = 0; C < D; ++C)
            G[Kc[C]] = Pe(n, d + C * 3, 7);
          d += D * 3;
          for (var E = Bs(G), Q = (1 << E) - 1, I = Zt(G, E, 1), C = 0; C < S; ) {
            var B = I[Pe(n, d, Q)];
            d += B & 15;
            var v = B >> 4;
            if (v < 16)
              P[C++] = v;
            else {
              var b = 0, T = 0;
              for (v == 16 ? (T = 3 + Pe(n, d, 3), d += 2, b = P[C - 1]) : v == 17 ? (T = 3 + Pe(n, d, 7), d += 3) : v == 18 && (T = 11 + Pe(n, d, 127), d += 7); T--; )
                P[C++] = b;
            }
          }
          var L = P.subarray(0, M), U = P.subarray(M);
          y = Bs(L), g = Bs(U), p = Zt(L, y, 1), m = Zt(U, g, 1);
        } else
          Re(1);
      else {
        var v = tl(d) + 4, x = n[v - 4] | n[v - 3] << 8, R = v + x;
        if (R > i) {
          l && Re(0);
          break;
        }
        r && A(h + x), t.set(n.subarray(v, R), h), e.b = h += x, e.p = d = R * 8, e.f = c;
        continue;
      }
      if (d > f) {
        l && Re(0);
        break;
      }
    }
    r && A(h + 131072);
    for (var _ = (1 << y) - 1, W = (1 << g) - 1, ee = d; ; ee = d) {
      var b = p[Ds(n, d) & _], z = b >> 4;
      if (d += b & 15, d > f) {
        l && Re(0);
        break;
      }
      if (b || Re(2), z < 256)
        t[h++] = z;
      else if (z == 256) {
        ee = d, p = null;
        break;
      } else {
        var O = z - 254;
        if (z > 264) {
          var C = z - 257, Y = ur[C];
          O = Pe(n, d, (1 << Y) - 1) + pr[C], d += Y;
        }
        var J = m[Ds(n, d) & W], se = J >> 4;
        J || Re(3), d += J & 15;
        var U = qc[se];
        if (se > 3) {
          var Y = hr[se];
          U += Ds(n, d) & (1 << Y) - 1, d += Y;
        }
        if (d > f) {
          l && Re(0);
          break;
        }
        r && A(h + 131072);
        var le = h + O;
        if (h < U) {
          var he = a - U, Me = Math.min(U, le);
          for (he + h < 0 && Re(3); h < Me; ++h)
            t[h] = s[he + h];
        }
        for (; h < le; ++h)
          t[h] = t[h - U];
      }
    }
    e.l = p, e.p = ee, e.b = h, e.f = c, p && (c = 1, e.m = y, e.d = m, e.n = g);
  } while (!c);
  return h != t.length && o ? Ri(t, 0, h) : t.subarray(0, h);
}, il = /* @__PURE__ */ new De(0), Ge = function(n, e) {
  return n[e] | n[e + 1] << 8;
}, _e = function(n, e) {
  return (n[e] | n[e + 1] << 8 | n[e + 2] << 16 | n[e + 3] << 24) >>> 0;
}, ks = function(n, e) {
  return _e(n, e) + _e(n, e + 4) * 4294967296;
};
function ol(n, e) {
  return sl(n, { i: 2 }, e && e.out, e && e.dictionary);
}
var ui = typeof TextDecoder < "u" && /* @__PURE__ */ new TextDecoder(), al = 0;
try {
  ui.decode(il, { stream: !0 }), al = 1;
} catch {
}
var rl = function(n) {
  for (var e = "", t = 0; ; ) {
    var s = n[t++], i = (s > 127) + (s > 223) + (s > 239);
    if (t + i > n.length)
      return { s: e, r: Ri(n, t - 1) };
    i ? i == 3 ? (s = ((s & 15) << 18 | (n[t++] & 63) << 12 | (n[t++] & 63) << 6 | n[t++] & 63) - 65536, e += String.fromCharCode(55296 | s >> 10, 56320 | s & 1023)) : i & 1 ? e += String.fromCharCode((s & 31) << 6 | n[t++] & 63) : e += String.fromCharCode((s & 15) << 12 | (n[t++] & 63) << 6 | n[t++] & 63) : e += String.fromCharCode(s);
  }
};
function cl(n, e) {
  if (e) {
    for (var t = "", s = 0; s < n.length; s += 16384)
      t += String.fromCharCode.apply(null, n.subarray(s, s + 16384));
    return t;
  } else {
    if (ui)
      return ui.decode(n);
    var i = rl(n), a = i.s, t = i.r;
    return t.length && Re(8), a;
  }
}
var ll = function(n, e) {
  return e + 30 + Ge(n, e + 26) + Ge(n, e + 28);
}, Al = function(n, e, t) {
  var s = Ge(n, e + 28), i = cl(n.subarray(e + 46, e + 46 + s), !(Ge(n, e + 8) & 2048)), a = e + 46 + s, o = _e(n, e + 20), r = t && o == 4294967295 ? dl(n, a) : [o, _e(n, e + 24), _e(n, e + 42)], l = r[0], A = r[1], c = r[2];
  return [Ge(n, e + 10), l, A, i, a + Ge(n, e + 30) + Ge(n, e + 32), c];
}, dl = function(n, e) {
  for (; Ge(n, e) != 1; e += 4 + Ge(n, e + 2))
    ;
  return [ks(n, e + 12), ks(n, e + 4), ks(n, e + 20)];
};
function ul(n, e) {
  for (var t = {}, s = n.length - 22; _e(n, s) != 101010256; --s)
    (!s || n.length - s > 65558) && Re(13);
  var i = Ge(n, s + 8);
  if (!i)
    return {};
  var a = _e(n, s + 16), o = a == 4294967295 || i == 65535;
  if (o) {
    var r = _e(n, s - 12);
    o = _e(n, r) == 101075792, o && (i = _e(n, r + 32), a = _e(n, r + 48));
  }
  for (var l = 0; l < i; ++l) {
    var A = Al(n, a, o), c = A[0], d = A[1], h = A[2], p = A[3], m = A[4], y = A[5], g = ll(n, y);
    a = m, c ? c == 8 ? t[p] = ol(n.subarray(g, g + d), { out: new De(h) }) : Re(14, "unknown compression type " + c) : t[p] = Ri(n, g, g + d);
  }
  return t;
}
function Li(n) {
  let e = !1;
  const t = (h, p, m) => n.on(h, p, m), s = (h, ...p) => n.fire(h, ...p);
  t(Qe, () => s(F).debugMode), t(Ei, () => e = !0);
  let i = 0;
  (function h() {
    i++, !e && requestAnimationFrame(h);
  })(), t(Rt, (h, p = null, m = 0) => {
    const y = () => {
      e || (m > 0 ? !(i % m) && h(i) : h(i), p && p() && requestAnimationFrame(y));
    };
    y();
  }), t(ha, (h, p = null, m = 20) => {
    const y = () => {
      e || (h(), p && p() && setTimeout(y, m));
    };
    y();
  });
  let a = !1, o = 0;
  t(Si, () => {
    if (a = !0, (async () => {
      const h = document.querySelector("#gsviewer #progressBarWrap");
      if (h) {
        h.style.display = "block";
        const p = document.querySelector("#gsviewer #progressBar");
        p && (p.style.width = "0%");
      }
    })(), (async () => document.querySelector("#gsviewer .logo")?.classList.add("loading"))(), parent?.onProgress && parent.onProgress(1e-3, "0.001%"), window.onProgress && window.onProgress(1e-3, "0.001%"), s(F).debugMode)
      (async () => {
        const h = document.querySelector("#gsviewer #progressBarWrap");
        if (h) {
          h.style.display = "block";
          const p = document.querySelector("#gsviewer #progressBar");
          p && (p.style.width = "0%");
        }
      })(), (async () => document.querySelector("#gsviewer .logo")?.classList.add("loading"))();
    else if (s(F).useCustomControl === !1)
      try {
        parent?.onProgress && parent.onProgress(1e-3, "0.001%");
      } catch (p) {
        p.message;
      }
  }), t(Jt, (h) => {
    if (a = !1, h !== void 0 && ((async () => {
      const m = document.querySelector("#gsviewer #progressBarWrap");
      m && (m.style.display = "none");
    })(), (async () => document.querySelector("#gsviewer .logo")?.classList.remove("loading"))(), parent?.onProgress && parent.onProgress(0, "100%", 9), window.onProgress && window.onProgress(0, "100%", 9), s(F).useCustomControl === !1))
      try {
        parent?.onProgress && parent.onProgress(0, "100%", 9);
      } catch (m) {
        m.message;
      }
  }), t(Xn, (h) => {
    if (a = !0, (async () => {
      const p = document.querySelector("#gsviewer #progressBar");
      p && (p.style.width = `${h}%`);
    })(), parent?.onProgress && parent.onProgress(h, `${h}%`), window.onProgress && window.onProgress(h, `${h}%`), s(F).debugMode)
      (async () => {
        const p = document.querySelector("#gsviewer #progressBar");
        p && (p.style.width = `${h}%`);
      })();
    else if (s(F).useCustomControl === !1)
      try {
        parent?.onProgress && parent.onProgress(h, `${h}%`);
      } catch (m) {
        m.message;
      }
  }), t(Pa, () => a), t(_a, (h) => {
    o = h;
  }), t(Ua, () => o), t(js, () => !a && o > 0), t(It, () => {
    const p = s(_t).parentElement.getBoundingClientRect();
    return { width: p.width, height: p.height, left: p.left, top: p.top };
  }), t(mt, (h) => {
    let p = h.x.toFixed(3).split("."), m = h.y.toFixed(3).split("."), y = h.z.toFixed(3).split(".");
    return (p[1] === "000" || p[1] === "00000") && (p[1] = "0"), (m[1] === "000" || m[1] === "00000") && (m[1] = "0"), (y[1] === "000" || y[1] === "00000") && (y[1] = "0"), `${p.join(".")}, ${m.join(".")}, ${y.join(".")}`;
  }), t($r, (h) => btoa(h)), t(ec, (h) => atob(h));
  const r = 1e-3;
  let l = new k(), A = new k(), c = 0;
  t(sc, () => {
    const h = s($), p = h.fov, m = h.position.clone(), y = h.getWorldDirection(new k());
    return Math.abs(c - p) < r && Math.abs(m.x - l.x) < r && Math.abs(m.y - l.y) < r && Math.abs(m.z - l.z) < r && Math.abs(y.x - A.x) < r && Math.abs(y.y - A.y) < r && Math.abs(y.z - A.z) < r ? !1 : (c = p, l = m, A = y, !0);
  }), t(tt, (h) => {
    if (!h) return;
    const p = [];
    h.traverse((m) => p.push(m)), p.forEach((m) => {
      m.dispose ? m.dispose() : (m.geometry?.dispose?.(), m.material && m.material instanceof Array ? m.material.forEach((y) => y?.dispose?.()) : m.material?.dispose?.());
    }), h.clear();
  }), t(
    ce,
    async ({
      renderSplatCount: h,
      visibleSplatCount: p,
      modelSplatCount: m,
      fps: y,
      realFps: g,
      sortTime: f,
      bucketBits: u,
      sortType: v,
      qualityLevel: x,
      fov: R,
      position: M,
      lookUp: D,
      lookAt: S,
      worker: P,
      scene: G,
      // updateSceneData,
      scale: C,
      cuts: E,
      shDegree: Q
    } = {}) => {
      if (!s(Qe)) return;
      Q !== void 0 && d("shDegree", `${Q}`), h !== void 0 && d("renderSplatCount", `${h}`), p !== void 0 && d("visibleSplatCount", `${p}`), m !== void 0 && d("modelSplatCount", `${m}`), y !== void 0 && d("fps", y), g !== void 0 && d("realFps", `raw ${g}`), f !== void 0 && d("sort", `${f} ms （L ${s(F).qualityLevel || Be.Default5}, ${u} B, T ${v}）`), E !== void 0 && d("cuts", E === "" ? "" : `（${E} cuts）`), P && d("worker", `${P}`), G && d("scene", G), R && d("fov", R), M && d("position", M), D && d("lookUp", D), S && d("lookAt", S), S && d("viewer-version", ds);
      let I = performance.memory || { usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0 }, B = "", b = I.usedJSHeapSize / 1024 / 1024;
      b > 1e3 ? B += (b / 1024).toFixed(2) + " G" : B += b.toFixed(0) + " M", B += " / ";
      let T = I.totalJSHeapSize / 1024 / 1024;
      T > 1e3 ? B += (T / 1024).toFixed(2) + " G" : B += T.toFixed(0) + " M";
      let L = I.jsHeapSizeLimit / 1024 / 1024;
      B += " / ", L > 1e3 ? B += (L / 1024).toFixed(2) + " G" : B += L.toFixed(0) + " M", d("memory", B), C && d("scale", C);
    }
  );
  async function d(h, p) {
    let m = document.querySelector(`#gsviewer .debug .${h}`);
    m && (m.innerText = p);
  }
}
const Yt = Tr;
function hl(n = 0) {
  const e = /* @__PURE__ */ new Date();
  return e.setDate(e.getDate() - 7), n >= e.getFullYear() * 1e4 + (e.getMonth() + 1) * 100 + e.getDate();
}
function Z(n) {
  return n < 0 ? 0 : n > 255 ? 255 : n | 0;
}
function vn(n) {
  const e = Z(Math.round(n * 128) + 128);
  return Z(Math.floor((e + 4) / 8) * 8);
}
function ss(n, e) {
  return ((1500 + n * 248) / e).toFixed(2) + "x";
}
async function Cr(n) {
  try {
    const e = new ReadableStream({
      async start(s) {
        s.enqueue(n), s.close();
      }
    }), t = new Response(e.pipeThrough(new DecompressionStream("gzip")));
    return new Uint8Array(await t.arrayBuffer());
  } catch (e) {
    return console.error("Decompress gzip failed:", e), null;
  }
}
async function gl(n) {
  try {
    const e = URL.createObjectURL(new Blob([n], { type: "application/octet-stream" })), t = await fetch(e), s = new Response(new Xc.XzReadableStream(t.body));
    return new Uint8Array(await s.arrayBuffer());
  } catch (e) {
    return console.error("Decompress xz failed:", e), null;
  }
}
async function fl(n) {
  const e = new Uint32Array(n.slice(0, 12).buffer), t = e[0], s = e[2];
  let i = 8;
  const a = n.slice(i + 4, i + 4 + s), { rgba: o } = await ue(a);
  i += 4 + s;
  const r = new Uint32Array(n.slice(i, i + 4).buffer)[0], l = n.slice(i + 4, i + 4 + r), { rgba: A } = await ue(l);
  i += 4 + r;
  const c = new Uint32Array(n.slice(i, i + 4).buffer)[0], d = n.slice(i + 4, i + 4 + c), { rgba: h } = await ue(d);
  i += 4 + c;
  const p = new Uint32Array(n.slice(i, i + 4).buffer)[0], m = n.slice(i + 4, i + 4 + p), { rgba: y } = await ue(m), g = new Uint8Array(8 + t * 19);
  let f = 0;
  g[f] = n[f++], g[f] = n[f++], g[f] = n[f++], g[f] = n[f++], g[f++] = 19, g[f++] = 0, g[f++] = 0, g[f++] = 0;
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 3];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 2];
  return g;
}
async function pl(n) {
  const e = new Uint32Array(n.slice(0, 16).buffer), t = e[0], s = e[3];
  let i = 12;
  const a = n.slice(i + 4, i + 4 + s), { rgba: o } = await ue(a);
  i += 4 + s;
  const r = new Uint32Array(n.slice(i, i + 4).buffer)[0], l = n.slice(i + 4, i + 4 + r), { rgba: A } = await ue(l);
  i += 4 + r;
  const c = new Uint32Array(n.slice(i, i + 4).buffer)[0], d = n.slice(i + 4, i + 4 + c), { rgba: h } = await ue(d);
  i += 4 + c;
  const p = new Uint32Array(n.slice(i, i + 4).buffer)[0], m = n.slice(i + 4, i + 4 + p), { rgba: y } = await ue(m), g = new Uint8Array(8 + t * 19);
  let f = 0;
  g[f] = n[f++], g[f] = n[f++], g[f] = n[f++], g[f] = n[f++], g[f++] = 35, g[f++] = 39, g[f++] = 0, g[f++] = 0, g[f] = n[f++], g[f] = n[f++], g[f] = n[f++], g[f] = n[f++];
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 4 + u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = o[t * 8 + u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = A[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 2];
  for (let u = 0; u < t; u++)
    g[f++] = h[u * 4 + 3];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 0];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 1];
  for (let u = 0; u < t; u++)
    g[f++] = y[u * 4 + 2];
  return g;
}
async function ml(n) {
  const t = new Uint32Array(n.slice(0, 8).buffer)[0], s = n.slice(8), { rgba: i } = await ue(s), a = new Uint8Array(8 + t * 9);
  let o = 0;
  a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o++] = 1, a[o++] = 0, a[o++] = 0, a[o++] = 0;
  for (let r = 0; r < t; r++)
    for (let l = 0; l < 3; l++)
      a[o++] = i[r * 60 + l * 4 + 0], a[o++] = i[r * 60 + l * 4 + 1], a[o++] = i[r * 60 + l * 4 + 2];
  return a;
}
async function ro(n) {
  const t = new Uint32Array(n.slice(0, 8).buffer)[0], s = n.slice(8), { rgba: i } = await ue(s), a = new Uint8Array(8 + t * 24);
  let o = 0;
  a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o++] = 2, a[o++] = 0, a[o++] = 0, a[o++] = 0;
  for (let r = 0; r < t; r++)
    for (let l = 0; l < 8; l++)
      a[o++] = i[r * 60 + l * 4 + 0], a[o++] = i[r * 60 + l * 4 + 1], a[o++] = i[r * 60 + l * 4 + 2];
  return a;
}
async function Cl(n) {
  const t = new Uint32Array(n.slice(0, 8).buffer)[0], s = n.slice(8), { rgba: i } = await ue(s), a = new Uint8Array(8 + t * 21);
  let o = 0;
  a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o] = n[o++], a[o++] = 3, a[o++] = 0, a[o++] = 0, a[o++] = 0;
  for (let r = 0; r < t; r++)
    for (let l = 8; l < 15; l++)
      a[o++] = i[r * 60 + l * 4 + 0], a[o++] = i[r * 60 + l * 4 + 1], a[o++] = i[r * 60 + l * 4 + 2];
  return a;
}
async function ue(n) {
  const e = new Blob([n], { type: "image/webp" }), t = URL.createObjectURL(e), s = await createImageBitmap(await fetch(t).then((A) => A.blob())), a = new OffscreenCanvas(s.width, s.height).getContext("2d");
  a.drawImage(s, 0, 0);
  const o = a.getImageData(0, 0, s.width, s.height), r = s.width, l = s.height;
  return s.close(), URL.revokeObjectURL(t), { width: r, height: l, rgba: o.data };
}
function yl(n) {
  const e = /* @__PURE__ */ new Map();
  try {
    const t = ul(n);
    for (const [s, i] of Object.entries(t))
      if (i) {
        const a = new Uint8Array(i);
        e.set(s, a);
      }
  } catch (t) {
    console.error("解压失败:", t);
  }
  return e;
}
function Il(n) {
  return new TextDecoder("utf-8").decode(n);
}
function co(n, e = null, t = 20) {
  const s = () => {
    n(), e?.() && setTimeout(s, t);
  };
  s();
}
const wl = "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", Fi = "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", Pi = "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";
async function El(n) {
  const e = new Uint32Array(n.buffer), t = new Float32Array(n.buffer), s = new Mc();
  s.Fixed = String.fromCharCode(n[0]) + String.fromCharCode(n[1]) + String.fromCharCode(n[2]), s.Version = n[3], s.SplatCount = e[1], s.MinX = t[2], s.MaxX = t[3], s.MinY = t[4], s.MaxY = t[5], s.MinZ = t[6], s.MaxZ = t[7], s.MinTopY = t[8], s.MaxTopY = t[9], s.CreateDate = e[10], s.CreaterId = e[11], s.ExclusiveId = e[12], s.ShDegree = n[52], s.Flag1 = n[53], s.Flag2 = n[54], s.Flag3 = n[55], s.Reserve1 = e[14], s.Reserve2 = e[15];
  let i = "";
  for (let h = 64; h < 124; h++)
    i += String.fromCharCode(n[h]);
  if (s.Comment = i.trim(), s.HashCheck = !0, s.Fixed !== "spx" && s.Version !== 1)
    return null;
  const a = s.ExclusiveId ? Fi : Pi, o = WebAssembly.compile(Uint8Array.from(atob(a), (h) => h.charCodeAt(0)).buffer), r = new WebAssembly.Memory({ initial: 1, maximum: 1 }), A = (await WebAssembly.instantiate(await o, { env: { memory: r, expf: us } })).exports.H;
  return new Uint8Array(r.buffer).set(n, 0), A(0) && (s.HashCheck = !1), s;
}
async function Se(n, e = null) {
  let t = new Uint32Array(n.slice(0, 8).buffer);
  const s = t[0], i = t[1];
  if (i == Nc) {
    if (e.ShDegree == en) {
      const a = await ml(n);
      return Gt(s, en, a, e.Version);
    } else if (e.ShDegree == Oe) {
      const a = await ro(n);
      return Gt(s, Oe, a, e.Version);
    } else if (e.ShDegree == wt) {
      const a = await ro(n), o = await Cl(n), r = await Gt(s, Oe, a, e.Version), l = await Gt(s, wt, o, e.Version);
      return r.success = r.success && l.success, r.isSh23 = !0, r.dataSh3 = l.datas, r;
    }
  }
  return i == _c ? n = await fl(n) : i == Uc && (n = await pl(n)), Gt(s, i, n, e?.Version || 1);
}
async function Gt(n, e, t, s) {
  const i = en == e, a = Oe == e, o = wt == e, r = i || a || o, l = !r, c = e < 65536 ? Pi : s > 1 ? Fi : wl, d = n * (r ? Tc : X), h = WebAssembly.compile(Uint8Array.from(atob(c), (v) => v.charCodeAt(0)).buffer), p = Math.floor((d + t.byteLength) / dr) + 2, m = new WebAssembly.Memory({ initial: p, maximum: p }), g = (await WebAssembly.instantiate(await h, { env: { memory: m, expf: us } })).exports.D, f = new Uint8Array(m.buffer);
  return f.set(t, d), g(0, d) ? { splatCount: n, blockFormat: e, success: !1 } : { splatCount: n, blockFormat: e, success: !0, datas: f.slice(0, d), isSplat: l, isSh: r, isSh1: i, isSh2: a, isSh3: o };
}
async function nn(n, e) {
  const t = WebAssembly.compile(Uint8Array.from(atob(Pi), (A) => A.charCodeAt(0)).buffer), s = Math.floor(e * X / dr) + 2, i = new WebAssembly.Memory({ initial: s, maximum: s }), o = (await WebAssembly.instantiate(await t, { env: { memory: i, expf: us } })).exports.s, r = new Uint8Array(i.buffer);
  r.set(n.slice(0, e * X), 0);
  const l = o(0, e);
  return l ? (console.error("splat data parser failed:", l), new Uint8Array(0)) : r.slice(0, e * X);
}
async function bl(n, e, t = !0, s = !0) {
  const i = WebAssembly.compile(Uint8Array.from(atob(Fi), (d) => d.charCodeAt(0)).buffer), a = new WebAssembly.Memory({ initial: 1, maximum: 1 }), r = (await WebAssembly.instantiate(await i, { env: { memory: a, expf: us } })).exports.w, l = new Uint8Array(a.buffer), A = new Float32Array(l.buffer), c = s ? -1 : 1;
  return A[0] = n, t ? A[1] = c * e : A[2] = c * e, r(0, t ? 1 : 0), l.slice(0, X);
}
function us(n) {
  return Math.exp(n);
}
const zt = Ce ? 20480 : 51200;
async function Sl(n) {
  try {
    n.status = N.Fetching;
    const i = n.abortController.signal, a = n.opts.fetchReload ? "reload" : "default", o = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: a, signal: i });
    if (o.status != 200) {
      console.warn(`fetch error: ${o.status}`), n.status === N.Fetching && (n.status = N.FetchFailed);
      return;
    }
    const r = o.body.getReader(), l = parseInt(o.headers.get("content-length") || "0");
    n.fileSize = l, n.downloadSize = 0, n.downloadSplatCount = 0, n.watermarkData = new Uint8Array(0);
    let A = new Uint8Array(256), c = 0, d = [], h;
    for (; ; ) {
      let { done: p, value: m } = await r.read();
      if (p) break;
      if (n.downloadSize += m.byteLength, d) {
        if (d.push(m), n.downloadSize < 200)
          continue;
        const y = new Uint8Array(n.downloadSize);
        for (let g = 0, f = 0; g < d.length; g++)
          y.set(d[g], f), f += d[g].byteLength;
        if (h = s(y), !h) {
          d = [y];
          continue;
        }
        d = null, m = y.slice(h.headerLength), n.rowLength = h.rowLength, n.dataShDegree = h.shDegree, n.modelSplatCount = h.vertexCount, n.splatData = new Uint8Array(Math.min(n.modelSplatCount, n.fetchLimit) * 32);
      }
      c + m.byteLength < n.rowLength ? (A.set(m, c), c += m.byteLength) : (c = await e(h, n, c, A, m), c && A.set(m.slice(m.byteLength - c), 0)), n.downloadSplatCount >= n.fetchLimit && n.abortController.abort();
    }
  } catch (i) {
    i.name === "AbortError" ? (n.opts.url, n.status === N.Fetching && (n.status = N.FetchAborted)) : (console.error(i), n.status === N.Fetching && (n.status = N.FetchFailed));
  } finally {
    n.status === N.Fetching && (n.status = N.FetchDone);
  }
  async function e(i, a, o, r, l) {
    return new Promise(async (A) => {
      let c = (o + l.byteLength) / a.rowLength | 0, d = (o + l.byteLength) % a.rowLength, h;
      o ? (h = new Uint8Array(c * a.rowLength), h.set(r.slice(0, o), 0), h.set(l.slice(0, l.byteLength - d), o)) : h = l.slice(0, c * a.rowLength), a.downloadSplatCount + c > a.fetchLimit && (c = a.fetchLimit - a.downloadSplatCount, d = 0);
      const p = async () => {
        if (c > zt) {
          const m = await t(i, h, zt);
          lo(a, m), a.downloadSplatCount += zt, c -= zt, h = h.slice(zt * a.rowLength), setTimeout(p);
        } else {
          const m = await t(i, h, c);
          lo(a, m), a.downloadSplatCount += c, A(d);
        }
      };
      await p();
    });
  }
  async function t(i, a, o) {
    const r = new Uint8Array(o * Wn), l = new Float32Array(a.buffer), A = new Float32Array(r.buffer);
    for (let c = 0; c < o; c++)
      A[c * 8 + 0] = l[(c * n.rowLength + i.offsets.x) / 4], A[c * 8 + 1] = l[(c * n.rowLength + i.offsets.y) / 4], A[c * 8 + 2] = l[(c * n.rowLength + i.offsets.z) / 4], A[c * 8 + 3] = Math.exp(l[(c * n.rowLength + i.offsets.scale_0) / 4]), A[c * 8 + 4] = Math.exp(l[(c * n.rowLength + i.offsets.scale_1) / 4]), A[c * 8 + 5] = Math.exp(l[(c * n.rowLength + i.offsets.scale_2) / 4]), r[c * 32 + 24] = Z((0.5 + Ye * l[(c * n.rowLength + i.offsets.f_dc_0) / 4]) * 255), r[c * 32 + 25] = Z((0.5 + Ye * l[(c * n.rowLength + i.offsets.f_dc_1) / 4]) * 255), r[c * 32 + 26] = Z((0.5 + Ye * l[(c * n.rowLength + i.offsets.f_dc_2) / 4]) * 255), r[c * 32 + 27] = Z(1 / (1 + Math.exp(-l[(c * n.rowLength + i.offsets.opacity) / 4])) * 255), r[c * 32 + 28] = Z(l[(c * n.rowLength + i.offsets.rot_0) / 4] * 128 + 128), r[c * 32 + 29] = Z(l[(c * n.rowLength + i.offsets.rot_1) / 4] * 128 + 128), r[c * 32 + 30] = Z(l[(c * n.rowLength + i.offsets.rot_2) / 4] * 128 + 128), r[c * 32 + 31] = Z(l[(c * n.rowLength + i.offsets.rot_3) / 4] * 128 + 128);
    if (i.shDegree == 3) {
      const d = new Uint8Array(o * 24 + 8), h = new Uint8Array(o * 21 + 8), p = new Uint32Array(2);
      p[0] = o, p[1] = Oe, d.set(new Uint8Array(p.buffer), 0);
      const m = new Uint32Array(2);
      m[0] = o, m[1] = wt, h.set(new Uint8Array(m.buffer), 0);
      for (let f = 0, u = 0; f < o; f++)
        for (let v = 0; v < 8; v++)
          for (let x = 0; x < 3; x++)
            d[8 + u++] = vn(l[(f * n.rowLength + i.offsets["f_rest_" + (v + x * 15)]) / 4]);
      for (let f = 0, u = 0; f < o; f++)
        for (let v = 8; v < 15; v++)
          for (let x = 0; x < 3; x++)
            h[8 + u++] = vn(l[(f * n.rowLength + i.offsets["f_rest_" + (v + x * 15)]) / 4]);
      const y = await Se(d);
      n.sh12Data.push(y.datas);
      const g = await Se(h);
      n.sh3Data.push(g.datas);
    } else if (i.shDegree == 2) {
      const d = new Uint8Array(o * 24 + 8), h = new Uint32Array(2);
      h[0] = o, h[1] = Oe, d.set(new Uint8Array(h.buffer), 0);
      for (let m = 0, y = 0; m < o; m++)
        for (let g = 0; g < 8; g++)
          for (let f = 0; f < 3; f++)
            d[8 + y++] = vn(l[(m * n.rowLength + i.offsets["f_rest_" + (g + f * 8)]) / 4]);
      const p = await Se(d);
      n.sh12Data.push(p.datas);
    } else if (i.shDegree == 1) {
      const d = new Uint8Array(o * 9 + 8), h = new Uint32Array(2);
      h[0] = o, h[1] = en, d.set(new Uint8Array(h.buffer), 0);
      for (let m = 0, y = 0; m < o; m++)
        for (let g = 0; g < 3; g++)
          for (let f = 0; f < 3; f++)
            d[8 + y++] = vn(l[(m * n.rowLength + i.offsets["f_rest_" + (g + f * 3)]) / 4]);
      const p = await Se(d);
      n.sh12Data.push(p.datas);
    }
    return await nn(r, o);
  }
  function s(i) {
    let a = new TextDecoder().decode(i.slice(0, 2048));
    const o = `end_header
`, r = a.indexOf(o);
    if (r < 0) {
      if (i.byteLength > 1024 * 2) throw new Error("Unable to read .ply file header");
      return null;
    }
    if (!a.startsWith("ply") || a.indexOf("format binary_little_endian 1.0") < 0)
      throw new Error("Unknow .ply file header");
    const l = parseInt(/element vertex (\d+)\n/.exec(a)[1]);
    let A = 0, c = {}, d = {};
    const h = {
      double: "getFloat64",
      int: "getInt32",
      uint: "getUint32",
      float: "getFloat32",
      short: "getInt16",
      ushort: "getUint16",
      uchar: "getUint8"
    };
    for (let y of a.slice(0, r).split(`
`).filter((g) => g.startsWith("property "))) {
      const [g, f, u] = y.split(" "), v = h[f] || "getInt8";
      d[u] = v, c[u] = A, A += parseInt(v.replace(/[^\d]/g, "")) / 8;
    }
    let p = 0;
    d.f_rest_44 ? p = 3 : d.f_rest_23 ? p = 2 : d.f_rest_8 && (p = 1);
    const m = ["x", "y", "z", "scale_0", "scale_1", "scale_2", "f_dc_0", "f_dc_1", "f_dc_2", "opacity", "rot_0", "rot_1", "rot_2", "rot_3"];
    for (let y = 0; y < m.length; y++) {
      const g = m[y];
      if (!d[g])
        throw new Error(`Property not found: ${g}`);
    }
    return { headerLength: r + o.length, offsets: c, rowLength: A, vertexCount: l, shDegree: p };
  }
}
function lo(n, e) {
  let t = e.byteLength / X;
  const s = Math.min(n.fetchLimit, n.modelSplatCount);
  n.dataSplatCount + t > s ? (t = s - n.dataSplatCount, n.splatData.set(e.slice(0, t * X), n.dataSplatCount * X)) : n.splatData.set(e, n.dataSplatCount * X);
  const i = new Float32Array(e.buffer);
  for (let o = 0, r = 0, l = 0, A = 0; o < t; o++)
    r = i[o * 8], l = i[o * 8 + 1], A = i[o * 8 + 2], n.minX = Math.min(n.minX, r), n.maxX = Math.max(n.maxX, r), n.minY = Math.min(n.minY, l), n.maxY = Math.max(n.maxY, l), n.minZ = Math.min(n.minZ, A), n.maxZ = Math.max(n.maxZ, A);
  n.dataSplatCount += t;
  const a = 0;
  n.currentRadius = Math.sqrt(n.maxX * n.maxX + a * a + n.maxZ * n.maxZ), n.aabbCenter = new k((n.minX + n.maxX) / 2, (n.minY + n.maxY) / 2, (n.minZ + n.maxZ) / 2), n.maxRadius = 0.5 * Math.sqrt(Math.pow(n.maxX - n.minX, 2) + Math.pow(n.maxY - n.minY, 2) + Math.pow(n.maxZ - n.minZ, 2)), n.metaMatrix && n.aabbCenter.applyMatrix4(n.metaMatrix);
}
const vt = Ce ? 20480 : 51200;
async function vl(n) {
  let e = 0;
  try {
    n.status = N.Fetching;
    const s = n.abortController.signal, i = n.opts.fetchReload ? "reload" : "default", a = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: i, signal: s });
    if (a.status != 200) {
      console.warn(`fetch error: ${a.status}`), n.status === N.Fetching && (n.status = N.FetchFailed);
      return;
    }
    const o = a.body.getReader(), r = parseInt(a.headers.get("content-length") || "0");
    n.rowLength = 32, n.fileSize = r;
    const l = r / n.rowLength | 0;
    if (l < 1) {
      console.warn("data empty", n.opts.url), n.status === N.Fetching && (n.status = N.Invalid);
      return;
    }
    n.CompressionRatio = "7.75x", n.modelSplatCount = l, n.downloadSplatCount = 0, n.splatData = new Uint8Array(Math.min(n.modelSplatCount, n.fetchLimit) * 32), n.watermarkData = new Uint8Array(0);
    let A = new Uint8Array(32), c = 0;
    for (; ; ) {
      let { done: d, value: h } = await o.read();
      if (d) break;
      c + h.byteLength < n.rowLength ? (A.set(h, c), c += h.byteLength, e += h.length, n.downloadSize = e) : (c = await t(n, c, A, h), c && A.set(h.slice(h.byteLength - c), 0)), n.downloadSplatCount >= n.fetchLimit && n.abortController.abort();
    }
  } catch (s) {
    s.name === "AbortError" ? (n.opts.url, n.status === N.Fetching && (n.status = N.FetchAborted)) : (console.error(s), n.status === N.Fetching && (n.status = N.FetchFailed));
  } finally {
    n.status === N.Fetching && (n.status = N.FetchDone);
  }
  async function t(s, i, a, o) {
    return new Promise(async (r) => {
      let l = (i + o.byteLength) / s.rowLength | 0, A = (i + o.byteLength) % s.rowLength, c;
      i ? (c = new Uint8Array(l * s.rowLength), c.set(a.slice(0, i), 0), c.set(o.slice(0, o.byteLength - A), i)) : c = o.slice(0, l * s.rowLength), s.downloadSplatCount + l > s.fetchLimit && (l = s.fetchLimit - s.downloadSplatCount, A = 0);
      const d = async () => {
        if (l > vt) {
          const h = await nn(c, vt);
          Ao(s, h), s.downloadSplatCount += vt, e += vt * s.rowLength, s.downloadSize = e, l -= vt, c = c.slice(vt * s.rowLength), setTimeout(d);
        } else {
          const h = await nn(c, l);
          Ao(s, h), s.downloadSplatCount += l, e += l * s.rowLength, s.downloadSize = e, r(A);
        }
      };
      await d();
    });
  }
}
function Ao(n, e) {
  let t = e.byteLength / X;
  const s = Math.min(n.fetchLimit, n.modelSplatCount);
  n.dataSplatCount + t > s ? (t = s - n.dataSplatCount, n.splatData.set(e.slice(0, t * X), n.dataSplatCount * X)) : n.splatData.set(e, n.dataSplatCount * X);
  const i = new Float32Array(e.buffer);
  for (let o = 0, r = 0, l = 0, A = 0; o < t; o++)
    r = i[o * 8], l = i[o * 8 + 1], A = i[o * 8 + 2], n.minX = Math.min(n.minX, r), n.maxX = Math.max(n.maxX, r), n.minY = Math.min(n.minY, l), n.maxY = Math.max(n.maxY, l), n.minZ = Math.min(n.minZ, A), n.maxZ = Math.max(n.maxZ, A);
  n.dataSplatCount += t;
  const a = n.header?.MinTopY || 0;
  n.currentRadius = Math.sqrt(n.maxX * n.maxX + a * a + n.maxZ * n.maxZ), n.aabbCenter = new k((n.minX + n.maxX) / 2, (n.minY + n.maxY) / 2, (n.minZ + n.maxZ) / 2), n.maxRadius = 0.5 * Math.sqrt(Math.pow(n.maxX - n.minX, 2) + Math.pow(n.maxY - n.minY, 2) + Math.pow(n.maxZ - n.minZ, 2)), n.metaMatrix && n.aabbCenter.applyMatrix4(n.metaMatrix);
}
const Ml = [Fc, Wc];
async function xl(n) {
  try {
    n.status = N.Fetching;
    const e = n.abortController.signal, t = n.opts.fetchReload ? "reload" : "default", s = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: t, signal: e });
    if (s.status != 200) {
      console.warn(`fetch error: ${s.status}`), n.status === N.Fetching && (n.status = N.FetchFailed);
      return;
    }
    const i = s.body.getReader(), a = parseInt(s.headers.get("content-length") || "0");
    if (a - St < kc) {
      console.warn("data empty", n.opts.url), n.status === N.Fetching && (n.status = N.Invalid);
      return;
    }
    n.fileSize = a;
    let r = [], l = new Uint8Array(St), A = new Uint8Array(X), c = 0, d = !1, h = 0, p = 0, m, y = !1, g = 0;
    for (; ; ) {
      let { done: f, value: u } = await i.read();
      if (f) break;
      if (n.downloadSize += u.byteLength, r) {
        r.push(u);
        let R = 0;
        for (let S = 0; S < r.length; S++)
          R += r[S].byteLength;
        if (R < St)
          continue;
        let M = 0;
        for (let S = 0; S < r.length; S++)
          M + r[S].byteLength < St ? (l.set(r[S], M), M += r[S].byteLength) : (l.set(r[S].slice(0, St - M), M), u = new Uint8Array(r[S].slice(St - M)));
        const D = await El(l);
        if (!D) {
          n.abortController.abort(), n.status === N.Fetching && (n.status = N.Invalid), console.error("invalid spx format");
          continue;
        }
        if (n.meta.autoCut > 1 && !Bl(D)) {
          n.abortController.abort(), n.status === N.Fetching && (n.status = N.Invalid), console.error("invalid LOD format");
          continue;
        }
        if (n.header = D, n.CompressionRatio = ss(D.SplatCount, a), n.modelSplatCount = D.SplatCount, n.dataShDegree = D.ShDegree, n.aabbCenter = new k((D.MinX + D.MaxX) / 2, (D.MinY + D.MaxY) / 2, (D.MinZ + D.MaxZ) / 2), n.maxRadius = 0.5 * Math.sqrt(Math.pow(D.MaxX - D.MinX, 2) + Math.pow(D.MaxY - D.MinY, 2) + Math.pow(D.MaxZ - D.MinZ, 2)), n.metaMatrix && n.aabbCenter.applyMatrix4(n.metaMatrix), r = null, l = null, !Ml.includes(D.ExclusiveId)) {
          n.abortController.abort(), n.status = N.Invalid, console.error("Unrecognized format, creater id =", D.CreaterId, ", exclusive id =", D.ExclusiveId, D.Comment);
          continue;
        }
      }
      if (!d) {
        if (c + u.byteLength < 4) {
          A.set(u, c), c += u.byteLength;
          continue;
        }
        const R = new Uint8Array(c + u.byteLength);
        R.set(A.slice(0, c), 0), R.set(u, c), u = R.slice(4), c = 0, d = !0, m = [], p = 0;
        const M = new Int32Array(R.slice(0, 4).buffer)[0];
        y = M < 0, g = Math.abs(M) >> 28 >>> 0, h = Math.abs(M) << 4 >>> 4;
      }
      let v = p + u.byteLength;
      if (m.push(u), v < h) {
        p += u.byteLength;
        continue;
      }
      for (; v >= h; ) {
        let R = new Uint8Array(h), M = 0;
        for (let S = 0; S < m.length; S++)
          M + m[S].byteLength < h ? (R.set(m[S], M), M += m[S].byteLength) : (R.set(m[S].slice(0, h - M), M), u = new Uint8Array(m[S].slice(h - M)));
        y && (g === 0 ? R = await Cr(R) : g === 1 ? R = await gl(R) : console.error("unsuported compress type:", g));
        const D = await Se(R, n.header);
        if (!D.success) {
          console.error("spx block data parser failed. block format:", D.blockFormat), n.abortController.abort(), n.status = N.Invalid;
          break;
        }
        if (D.isSplat)
          n.downloadSplatCount += D.splatCount, Ql(n, D.datas);
        else {
          const S = Math.min(n.fetchLimit, n.modelSplatCount);
          if (D.isSh23) {
            if (n.sh12Count + D.splatCount > S) {
              const P = S - n.sh12Count;
              n.sh12Data.push(D.datas.slice(0, P * 16)), n.sh12Count += P;
            } else
              n.sh12Data.push(D.datas), n.sh12Count += D.splatCount;
            if (n.sh3Count + D.splatCount > S) {
              const P = S - n.sh3Count;
              n.sh3Data.push(D.dataSh3.slice(0, P * 16)), n.sh3Count += P;
            } else
              n.sh3Data.push(D.dataSh3), n.sh3Count += D.splatCount;
          } else if (D.isSh3)
            if (n.sh3Count + D.splatCount > S) {
              const P = S - n.sh3Count;
              n.sh3Data.push(D.datas.slice(0, P * 16)), n.sh3Count += P;
            } else
              n.sh3Data.push(D.datas), n.sh3Count += D.splatCount;
          else if (n.sh12Count + D.splatCount > S) {
            const P = S - n.sh12Count;
            n.sh12Data.push(D.datas.slice(0, P * 16)), n.sh12Count += P;
          } else
            n.sh12Data.push(D.datas), n.sh12Count += D.splatCount;
        }
        if (u.byteLength < 4) {
          A.set(u, 0), c = u.byteLength, d = !1;
          break;
        } else {
          const S = new Int32Array(u.slice(0, 4).buffer)[0];
          y = S < 0, g = Math.abs(S) >> 28 >>> 0, h = Math.abs(S) << 4 >>> 4, u = u.slice(4), v = u.byteLength, m = [u], p = u.byteLength;
        }
      }
      const x = n.fetchLimit;
      n.header.ShDegree === 3 ? n.downloadSplatCount >= x && n.sh12Count >= x && n.sh3Count >= x && n.abortController.abort() : n.header.ShDegree ? n.downloadSplatCount >= x && n.sh12Count >= x && n.abortController.abort() : n.downloadSplatCount >= x && n.abortController.abort();
    }
  } catch (e) {
    e.name === "AbortError" ? (console.warn("Fetch Abort", n.opts.url), n.status === N.Fetching && (n.status = N.FetchAborted)) : (console.error(e), n.status === N.Fetching && (n.status = N.FetchFailed), n.abortController.abort());
  } finally {
    n.status === N.Fetching && (n.status = N.FetchDone);
  }
}
function Ql(n, e) {
  let t = !!n.meta.autoCut, s = e.byteLength / 32;
  const i = 4096;
  if (!t) {
    const l = Math.min(n.fetchLimit, n.modelSplatCount);
    if (!n.splatData && (n.splatData = new Uint8Array(l * X)), !n.watermarkData && (n.watermarkData = new Uint8Array(0)), n.dataSplatCount + s > l && (s = l - n.dataSplatCount, !s))
      return;
    const A = new Float32Array(e.buffer), c = new Uint32Array(e.buffer);
    for (let h = 0, p = 0, m = 0, y = 0; h < s; h++)
      if (p = A[h * 8], m = A[h * 8 + 1], y = A[h * 8 + 2], n.minX = Math.min(n.minX, p), n.maxX = Math.max(n.maxX, p), n.minY = Math.min(n.minY, m), n.maxY = Math.max(n.maxY, m), n.minZ = Math.min(n.minZ, y), n.maxZ = Math.max(n.maxZ, y), c[h * 8 + 3] >> 16) {
        if (n.watermarkCount * X === n.watermarkData.byteLength) {
          const g = new Uint8Array((n.watermarkCount + i) * X);
          g.set(n.watermarkData, 0), n.watermarkData = g;
        }
        n.watermarkData.set(e.slice(h * 32, h * 32 + 32), n.watermarkCount++ * X);
      } else
        n.splatData.set(e.slice(h * 32, h * 32 + 32), n.dataSplatCount++ * X);
    const d = n.header.MinTopY || 0;
    n.currentRadius = Math.sqrt(n.maxX * n.maxX + d * d + n.maxZ * n.maxZ);
    return;
  }
  let a = Math.min(Math.max(n.meta.autoCut, 2), 50);
  !n.watermarkData && (n.watermarkData = new Uint8Array(0));
  const o = new Float32Array(e.buffer), r = new Uint32Array(e.buffer);
  for (let l = 0, A = Math.floor(e.byteLength / X), c = 0, d = 0, h = 0, p = ""; l < A; l++) {
    if (r[l * 8 + 3] >> 16) {
      if (n.watermarkCount * X === n.watermarkData.byteLength) {
        const f = new Uint8Array((n.watermarkCount + i) * X);
        f.set(n.watermarkData, 0), n.watermarkData = f;
      }
      n.watermarkData.set(e.slice(l * 32, l * 32 + 32), n.watermarkCount++ * X);
      continue;
    }
    c = o[l * 8], d = o[l * 8 + 1], h = o[l * 8 + 2];
    let m = Math.min(a - 1, Math.floor(Math.max(0, c - n.header.MinX) / (n.header.MaxX - n.header.MinX) * a)), y = Math.min(a - 1, Math.floor(Math.max(0, h - n.header.MinZ) / (n.header.MaxZ - n.header.MinZ) * a));
    p = `${m}-${y}`;
    let g = n.map.get(p);
    if (!g)
      g = {}, g.minX = c, g.maxX = c, g.minY = d, g.maxY = d, g.minZ = h, g.maxZ = h, g.center = new k(c, d, h), n.metaMatrix && g.center.applyMatrix4(n.metaMatrix), g.radius = 0, g.splatData = new Uint8Array(i * X), g.splatData.set(e.slice(l * X, l * X + X), 0), g.splatCount = 1, n.map.set(p, g);
    else {
      if (g.splatData.byteLength / X == g.splatCount) {
        const x = new Uint8Array(g.splatData.byteLength + i * X);
        x.set(g.splatData, 0), g.splatData = x;
      }
      g.minX = Math.min(g.minX, c), g.maxX = Math.max(g.maxX, c), g.minY = Math.min(g.minY, d), g.maxY = Math.max(g.maxY, d), g.minZ = Math.min(g.minZ, h), g.maxZ = Math.max(g.maxZ, h), g.center = new k((g.maxX + g.minX) / 2, (g.maxY + g.minY) / 2, (g.maxZ + g.minZ) / 2), n.metaMatrix && g.center.applyMatrix4(n.metaMatrix);
      const f = g.maxX - g.minX, u = g.maxY - g.minY, v = g.maxZ - g.minZ;
      g.radius = Math.sqrt(f * f + u * u + v * v) / 2, n.metaMatrix && (g.radius *= n.metaMatrix.getMaxScaleOnAxis()), g.splatData.set(e.slice(l * X, l * X + X), g.splatCount++ * X);
    }
    n.dataSplatCount++;
  }
}
function Bl(n) {
  return ((n?.Flag1 || 0) & 1) > 0;
}
const q = Ce ? 20480 : 51200, Ts = 16, uo = 511;
async function Dl(n) {
  try {
    n.status = N.Fetching;
    const i = n.abortController.signal, a = n.opts.fetchReload ? "reload" : "default", o = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: a, signal: i });
    if (o.status != 200) {
      console.warn(`fetch error: ${o.status}`), n.status === N.Fetching && (n.status = N.FetchFailed);
      return;
    }
    const r = o.body.getReader(), l = parseInt(o.headers.get("content-length") || "0");
    n.fileSize = l, n.downloadSize = 0, n.downloadSplatCount = 0, n.watermarkData = new Uint8Array(0);
    const A = new Uint8Array(l);
    for (; ; ) {
      let { done: h, value: p } = await r.read();
      if (h) break;
      A.set(p, n.downloadSize), n.downloadSize += p.length;
    }
    const c = await Cr(A);
    if (!c || c.length < 16) {
      console.error("Invalid spz format"), n.status = N.Invalid;
      return;
    }
    const d = s(c);
    n.CompressionRatio = ss(d.numPoints, l), n.spzVersion = d.version, n.modelSplatCount = d.numPoints, n.dataShDegree = d.shDegree, n.splatData = new Uint8Array(Math.min(n.modelSplatCount, n.fetchLimit) * 32), await e(d, n, c);
  } catch (i) {
    i.name === "AbortError" ? (n.opts.url, n.status === N.Fetching && (n.status = N.FetchAborted)) : (console.error(i), n.status === N.Fetching && (n.status = N.FetchFailed));
  } finally {
    n.status === N.Fetching && (n.status = N.FetchDone);
  }
  async function e(i, a, o) {
    const r = i.numPoints * 9, l = i.numPoints, A = i.numPoints * 3, c = i.numPoints * 3, d = i.numPoints * (i.version == 2 ? 3 : 4), h = Ts, p = h + r, m = p + l, y = m + A, g = y + c, f = g + d, u = Math.min(i.numPoints, a.fetchLimit), v = Math.ceil(u / q);
    for (let x = 0; x < v; x++) {
      let R = x < v - 1 ? q : u - x * q;
      a.dataSplatCount + R > a.fetchLimit && (R = a.fetchLimit - a.dataSplatCount);
      const M = new Uint8Array(R * 20 + 8), D = new Uint32Array(2);
      D[0] = R, D[1] = Pc, M.set(new Uint8Array(D.buffer), 0);
      let S = 8;
      for (let C = 0; C < R; C++)
        M[S++] = o[h + (x * q + C) * 9 + 0], M[S++] = o[h + (x * q + C) * 9 + 1], M[S++] = o[h + (x * q + C) * 9 + 2];
      for (let C = 0; C < R; C++)
        M[S++] = o[h + (x * q + C) * 9 + 3], M[S++] = o[h + (x * q + C) * 9 + 4], M[S++] = o[h + (x * q + C) * 9 + 5];
      for (let C = 0; C < R; C++)
        M[S++] = o[h + (x * q + C) * 9 + 6], M[S++] = o[h + (x * q + C) * 9 + 7], M[S++] = o[h + (x * q + C) * 9 + 8];
      for (let C = 0; C < R; C++)
        M[S++] = o[y + (x * q + C) * 3];
      for (let C = 0; C < R; C++)
        M[S++] = o[y + (x * q + C) * 3 + 1];
      for (let C = 0; C < R; C++)
        M[S++] = o[y + (x * q + C) * 3 + 2];
      for (let C = 0; C < R; C++)
        M[S++] = Rs(o[m + (x * q + C) * 3]);
      for (let C = 0; C < R; C++)
        M[S++] = Rs(o[m + (x * q + C) * 3 + 1]);
      for (let C = 0; C < R; C++)
        M[S++] = Rs(o[m + (x * q + C) * 3 + 2]);
      const P = [];
      if (i.version == 2)
        for (let C = 0, E = 0, Q = 0, I = 0; C < R; C++)
          M[S++] = o[p + (x * q + C)], E = o[g + (x * q + C) * 3 + 0], Q = o[g + (x * q + C) * 3 + 1], I = o[g + (x * q + C) * 3 + 2], P.push(kl(E, Q, I));
      else
        for (let C = 0, E = 0, Q = 0, I = 0, B = 0; C < R; C++) {
          M[S++] = o[p + (x * q + C)], E = o[g + (x * q + C) * 4 + 0], Q = o[g + (x * q + C) * 4 + 1], I = o[g + (x * q + C) * 4 + 2], B = o[g + (x * q + C) * 4 + 3];
          const b = E | Q << 8 | I << 16 | B << 24, T = b >>> 30;
          let L = b, U = 0, _ = [];
          for (let W = 3; W >= 0; W--)
            if (W !== T) {
              const ee = L & uo, z = (L >> 9 & 1) > 0;
              _[W] = Math.SQRT1_2 * (ee / uo), z && (_[W] = -_[W]), U += _[W] * _[W], L = L >> 10;
            }
          _[T] = Math.sqrt(Math.max(1 - U, 0));
          for (let W = 0; W < 4; W++)
            _[W] = Z(_[W] * 128 + 128);
          P.push(_);
        }
      for (let C = 0; C < R; C++)
        M[S++] = P[C][0];
      for (let C = 0; C < R; C++)
        M[S++] = P[C][1];
      for (let C = 0; C < R; C++)
        M[S++] = P[C][2];
      for (let C = 0; C < R; C++)
        M[S++] = P[C][3];
      const G = await Se(M);
      if (t(i, a, G.datas), i.shDegree === 1) {
        const C = new Uint8Array(R * 9 + 8), E = new Uint32Array(2);
        E[0] = R, E[1] = en, C.set(new Uint8Array(E.buffer), 0);
        for (let I = 0, B = 8; I < R; I++)
          C.set(o.slice(f + (x * q + I) * 9, f + (x * q + I) * 9 + 9), B), B += 9;
        const Q = await Se(C);
        a.sh12Data.push(Q.datas);
      } else if (i.shDegree === 2) {
        const C = new Uint8Array(R * 24 + 8), E = new Uint32Array(2);
        E[0] = R, E[1] = Oe, C.set(new Uint8Array(E.buffer), 0);
        for (let I = 0, B = 8; I < R; I++)
          C.set(o.slice(f + (x * q + I) * 24, f + (x * q + I) * 24 + 24), B), B += 24;
        const Q = await Se(C);
        a.sh12Data.push(Q.datas);
      } else if (i.shDegree === 3) {
        const C = new Uint8Array(R * 24 + 8), E = new Uint32Array(2);
        E[0] = R, E[1] = Oe, C.set(new Uint8Array(E.buffer), 0);
        for (let T = 0, L = 8; T < R; T++)
          C.set(o.slice(f + (x * q + T) * 45, f + (x * q + T) * 45 + 24), L), L += 24;
        const Q = await Se(C);
        a.sh12Data.push(Q.datas);
        const I = new Uint8Array(R * 21 + 8), B = new Uint32Array(2);
        B[0] = R, B[1] = wt, I.set(new Uint8Array(B.buffer), 0);
        for (let T = 0, L = 8; T < R; T++)
          I.set(o.slice(f + (x * q + T) * 45 + 24, f + (x * q + T) * 45 + 45), L), L += 21;
        const b = await Se(I);
        a.sh3Data.push(b.datas);
      }
      if (a.dataSplatCount >= a.fetchLimit)
        break;
    }
  }
  function t(i, a, o) {
    let r = o.byteLength / X;
    const l = Math.min(a.fetchLimit, i.numPoints);
    a.dataSplatCount + r > l ? (r = l - a.dataSplatCount, a.splatData.set(o.slice(0, r * X), a.dataSplatCount * X)) : a.splatData.set(o, a.dataSplatCount * X);
    const A = new Float32Array(o.buffer);
    for (let d = 0, h = 0, p = 0, m = 0; d < r; d++)
      h = A[d * 8], p = A[d * 8 + 1], m = A[d * 8 + 2], a.minX = Math.min(a.minX, h), a.maxX = Math.max(a.maxX, h), a.minY = Math.min(a.minY, p), a.maxY = Math.max(a.maxY, p), a.minZ = Math.min(a.minZ, m), a.maxZ = Math.max(a.maxZ, m);
    a.dataSplatCount += r, a.downloadSplatCount += r;
    const c = 0;
    a.currentRadius = Math.sqrt(a.maxX * a.maxX + c * c + a.maxZ * a.maxZ), a.aabbCenter = new k((a.minX + a.maxX) / 2, (a.minY + a.maxY) / 2, (a.minZ + a.maxZ) / 2), a.maxRadius = 0.5 * Math.sqrt(Math.pow(a.maxX - a.minX, 2) + Math.pow(a.maxY - a.minY, 2) + Math.pow(a.maxZ - a.minZ, 2)), a.metaMatrix && a.aabbCenter.applyMatrix4(a.metaMatrix);
  }
  function s(i) {
    const a = i.slice(0, Ts), o = new Uint32Array(a.buffer), r = {};
    if (r.magic = o[0], r.version = o[1], r.numPoints = o[2], r.shDegree = a[12], r.fractionalBits = a[13], r.flags = a[14], r.reserved = a[15], r.magic !== 1347635022) throw new Error("[SPZ ERROR] header not found");
    if (r.version < 2 || r.version > 3) throw new Error("[SPZ ERROR] version not supported:" + r.version);
    if (r.shDegree > 3) throw new Error("[SPZ ERROR] unsupported SH degree:" + r.shDegree);
    if (r.fractionalBits !== 12) throw new Error("[SPZ ERROR] unsupported FractionalBits:" + r.fractionalBits);
    const l = r.version == 2 ? 19 : 20;
    let A = 0;
    if (r.shDegree === 1 ? A = 9 : r.shDegree === 2 ? A = 24 : r.shDegree === 3 && (A = 45), i.length !== Ts + r.numPoints * (l + A)) throw new Error("[SPZ ERROR] invalid spz data");
    return r;
  }
}
function Rs(n) {
  const e = (n - 127.5) / 38.25;
  return Z((0.5 + Ye * e) * 255);
}
function kl(n, e, t) {
  const s = n / 127.5 - 1, i = e / 127.5 - 1, a = t / 127.5 - 1, o = Math.sqrt(Math.max(0, 1 - (s * s + i * i + a * a)));
  return [Z(o * 128 + 128), Z(s * 128 + 128), Z(i * 128 + 128), Z(a * 128 + 128)];
}
async function Ke(n, e) {
  if (!n) return new Uint8Array(0);
  const t = new AbortController(), s = t.signal;
  try {
    e?.fire(Si);
    const i = await fetch(n, { mode: "cors", credentials: "omit", cache: "reload", signal: s });
    if (i.status != 200)
      return console.warn(`fetch error: ${i.status}`), null;
    const a = i.body.getReader(), o = parseInt(i.headers.get("content-length") || "0"), r = new Uint8Array(o);
    let l = 0;
    for (; ; ) {
      let { done: A, value: c } = await a.read();
      if (A) break;
      r.set(c, l), l += c.length, e?.fire(Xn, 100 * l / o);
    }
    return r;
  } catch (i) {
    console.error(i), t.abort();
  }
  return null;
}
const ho = Ce ? 20480 : 51200, Mt = Math.sqrt(2);
async function Tl(n) {
  try {
    if (n.status = N.Fetching, n.opts.url.startsWith("blob:") || n.opts.url.endsWith(".sog")) {
      n.status = N.Fetching;
      const e = n.abortController.signal, t = n.opts.fetchReload ? "reload" : "default", s = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: t, signal: e });
      if (s.status != 200) {
        console.warn(`fetch error: ${s.status}`), n.status === N.Fetching && (n.status = N.FetchFailed);
        return;
      }
      const i = s.body.getReader(), a = parseInt(s.headers.get("content-length") || "0");
      n.fileSize = a, n.downloadSize = 0, n.downloadSplatCount = 0, n.watermarkData = new Uint8Array(0);
      const o = new Uint8Array(a);
      for (; ; ) {
        let { done: c, value: d } = await i.read();
        if (c) break;
        o.set(d, n.downloadSize), n.downloadSize += d.length;
      }
      const r = yl(o), l = JSON.parse(Il(r.get("meta.json"))), A = l.count || l.means.shape[0];
      n.modelSplatCount = A, n.CompressionRatio = ss(A, a), n.sogVersion = l.version ? l.version : 1, await go(n, r, l);
    } else {
      const e = await fetch(n.opts.url, { mode: "cors", credentials: "omit", cache: "reload" });
      if (e.status != 200) return console.error(`fetch error: ${e.status}`);
      const t = await e.json(), s = n.opts.url.split("/"), i = /* @__PURE__ */ new Map(), a = t.count || t.means.shape[0];
      n.modelSplatCount = a;
      const o = t.means.files[0];
      s[s.length - 1] = o;
      const r = s.join("/"), l = t.means.files[1];
      s[s.length - 1] = l;
      const A = s.join("/"), c = t.scales.files[0];
      s[s.length - 1] = c;
      const d = s.join("/"), h = t.quats.files[0];
      s[s.length - 1] = h;
      const p = s.join("/"), m = t.sh0.files[0];
      s[s.length - 1] = m;
      const y = s.join("/"), g = t.shN ? t.shN.files[0] : "shN_centroids.webp";
      s[s.length - 1] = g;
      const f = s.join("/"), u = t.shN ? t.shN.files[1] : "shN_labels.webp";
      s[s.length - 1] = u;
      const v = s.join("/"), [x, R, M, D, S, P, G] = await Promise.all([
        Ke(r),
        Ke(A),
        Ke(d),
        Ke(p),
        Ke(y),
        Ke(t.shN ? f : ""),
        Ke(t.shN ? v : "")
      ]);
      i.set(o, x), i.set(l, R), i.set(c, M), i.set(h, D), i.set(m, S), i.set(g, P), i.set(u, G);
      const C = JSON.stringify(t).length + x.length + R.length + M.length + D.length + S.length + P.length + G.length;
      n.fileSize = C, n.downloadSize = C, n.watermarkData = new Uint8Array(0), n.CompressionRatio = ss(a, C), n.sogVersion = t.version ? t.version : 1, await go(n, i, t);
    }
  } catch (e) {
    e.name === "AbortError" ? (n.opts.url, n.status === N.Fetching && (n.status = N.FetchAborted)) : (console.error(e), n.status === N.Fetching && (n.status = N.FetchFailed));
  } finally {
    n.status === N.Fetching && (n.status = N.FetchDone);
  }
}
async function go(n, e, t) {
  const s = !t.version, i = s ? t.means.shape[0] : t.count, a = Math.min(i, n.fetchLimit), o = t.shN ? 3 : 0, r = new Uint8Array(a * Wn);
  n.modelSplatCount = i, n.dataShDegree = o, n.splatData = r;
  const { rgba: l } = await ue(e.get(t.means.files[0])), { rgba: A } = await ue(e.get(t.means.files[1])), { rgba: c } = await ue(e.get(t.scales.files[0])), { rgba: d } = await ue(e.get(t.quats.files[0])), { rgba: h } = await ue(e.get(t.sh0.files[0])), { rgba: p, width: m } = t.shN ? await ue(e.get(t.shN.files[0])) : { rgba: null, width: 0 }, { rgba: y } = t.shN ? await ue(e.get(t.shN.files[1])) : { rgba: null };
  for (; a > n.dataSplatCount; ) {
    const v = s ? await g(n.dataSplatCount) : await f(n.dataSplatCount);
    n.splatData.set(v.slice(0), n.dataSplatCount * X), u(n, v), n.dataSplatCount += v.byteLength / X, n.downloadSplatCount = n.dataSplatCount;
  }
  async function g(v) {
    const x = Math.min(v + ho, a), R = x - v, M = new Uint8Array(R * Wn), D = new Float32Array(M.buffer);
    for (let S = v, P = 0; S < x; S++) {
      let G = (A[S * 4 + 0] << 8 | l[S * 4 + 0]) / 65535, C = (A[S * 4 + 1] << 8 | l[S * 4 + 1]) / 65535, E = (A[S * 4 + 2] << 8 | l[S * 4 + 2]) / 65535, Q = t.means.mins[0] + (t.means.maxs[0] - t.means.mins[0]) * G, I = t.means.mins[1] + (t.means.maxs[1] - t.means.mins[1]) * C, B = t.means.mins[2] + (t.means.maxs[2] - t.means.mins[2]) * E;
      Q = Math.sign(Q) * (Math.exp(Math.abs(Q)) - 1), I = Math.sign(I) * (Math.exp(Math.abs(I)) - 1), B = Math.sign(B) * (Math.exp(Math.abs(B)) - 1);
      let b = c[S * 4 + 0] / 255, T = c[S * 4 + 1] / 255, L = c[S * 4 + 2] / 255;
      b = Math.exp(t.scales.mins[0] + (t.scales.maxs[0] - t.scales.mins[0]) * b), T = Math.exp(t.scales.mins[1] + (t.scales.maxs[1] - t.scales.mins[1]) * T), L = Math.exp(t.scales.mins[2] + (t.scales.maxs[2] - t.scales.mins[2]) * L);
      let U = (d[S * 4 + 0] / 255 - 0.5) * Mt, _ = (d[S * 4 + 1] / 255 - 0.5) * Mt, W = (d[S * 4 + 2] / 255 - 0.5) * Mt, ee = Math.sqrt(Math.max(0, 1 - U * U - _ * _ - W * W)), z = d[S * 4 + 3] - 252, O, Y, J, se;
      z == 0 ? (O = ee, Y = U, J = _, se = W) : z == 1 ? (O = U, Y = ee, J = _, se = W) : z == 2 ? (O = U, Y = _, J = ee, se = W) : (O = U, Y = _, J = W, se = ee);
      let le = t.sh0.mins[0] + (t.sh0.maxs[0] - t.sh0.mins[0]) * (h[S * 4 + 0] / 255), he = t.sh0.mins[1] + (t.sh0.maxs[1] - t.sh0.mins[1]) * (h[S * 4 + 1] / 255), Me = t.sh0.mins[2] + (t.sh0.maxs[2] - t.sh0.mins[2]) * (h[S * 4 + 2] / 255), re = t.sh0.mins[3] + (t.sh0.maxs[3] - t.sh0.mins[3]) * (h[S * 4 + 3] / 255);
      D[P * 8 + 0] = Q, D[P * 8 + 1] = I, D[P * 8 + 2] = B, D[P * 8 + 3] = b, D[P * 8 + 4] = T, D[P * 8 + 5] = L, M[P * 32 + 24] = Z((0.5 + Ye * le) * 255), M[P * 32 + 25] = Z((0.5 + Ye * he) * 255), M[P * 32 + 26] = Z((0.5 + Ye * Me) * 255), M[P * 32 + 27] = Z(1 / (1 + Math.exp(-re)) * 255), M[P * 32 + 28] = Z(O * 128 + 128), M[P * 32 + 29] = Z(Y * 128 + 128), M[P * 32 + 30] = Z(J * 128 + 128), M[P * 32 + 31] = Z(se * 128 + 128), P++;
    }
    if (o > 0) {
      const S = new Uint32Array(2);
      S[0] = R, S[1] = Oe;
      const P = new Uint8Array(8 + R * 24);
      P.set(new Uint8Array(S.buffer), 0);
      const G = new Uint32Array(2);
      G[0] = R, G[1] = wt;
      const C = new Uint8Array(8 + R * 21);
      C.set(new Uint8Array(G.buffer), 0);
      for (let I = v, B = 0; I < x; I++) {
        const b = y[I * 4 + 0] + (y[I * 4 + 1] << 8), T = (b & 63) * 15, U = (b >> 6) * m + T, _ = new Uint8Array(9), W = new Uint8Array(15), ee = new Uint8Array(21);
        let z;
        for (let O = 0; O < 3; O++) {
          for (let Y = 0; Y < 3; Y++)
            z = (t.shN.maxs - t.shN.mins) * p[(U + Y) * 4 + O] / 255 + t.shN.mins, _[Y * 3 + O] = Z(Math.round(z * 128) + 128);
          for (let Y = 0; Y < 5; Y++)
            z = (t.shN.maxs - t.shN.mins) * p[(U + 3 + Y) * 4 + O] / 255 + t.shN.mins, W[Y * 3 + O] = Z(Math.round(z * 128) + 128);
          for (let Y = 0; Y < 7; Y++)
            z = (t.shN.maxs - t.shN.mins) * p[(U + 8 + Y) * 4 + O] / 255 + t.shN.mins, ee[Y * 3 + O] = Z(Math.round(z * 128) + 128);
        }
        P.set(_, 8 + B * 24), P.set(W, 8 + B * 24 + 9), C.set(ee, 8 + B * 21), B++;
      }
      const E = await Se(P);
      n.sh12Data.push(E.datas);
      const Q = await Se(C);
      n.sh3Data.push(Q.datas);
    }
    return await nn(M, R);
  }
  async function f(v) {
    const x = Math.min(v + ho, a), R = x - v, M = new Uint8Array(R * Wn), D = new Float32Array(M.buffer);
    for (let S = v, P = 0; S < x; S++) {
      let G = (A[S * 4 + 0] << 8 | l[S * 4 + 0]) / 65535, C = (A[S * 4 + 1] << 8 | l[S * 4 + 1]) / 65535, E = (A[S * 4 + 2] << 8 | l[S * 4 + 2]) / 65535, Q = t.means.mins[0] + (t.means.maxs[0] - t.means.mins[0]) * G, I = t.means.mins[1] + (t.means.maxs[1] - t.means.mins[1]) * C, B = t.means.mins[2] + (t.means.maxs[2] - t.means.mins[2]) * E;
      Q = Math.sign(Q) * (Math.exp(Math.abs(Q)) - 1), I = Math.sign(I) * (Math.exp(Math.abs(I)) - 1), B = Math.sign(B) * (Math.exp(Math.abs(B)) - 1);
      let b = Math.exp(t.scales.codebook[c[S * 4 + 0]]), T = Math.exp(t.scales.codebook[c[S * 4 + 1]]), L = Math.exp(t.scales.codebook[c[S * 4 + 2]]), U = (d[S * 4 + 0] / 255 - 0.5) * Mt, _ = (d[S * 4 + 1] / 255 - 0.5) * Mt, W = (d[S * 4 + 2] / 255 - 0.5) * Mt, ee = Math.sqrt(Math.max(0, 1 - U * U - _ * _ - W * W)), z = d[S * 4 + 3] - 252, O, Y, J, se;
      z == 0 ? (O = ee, Y = U, J = _, se = W) : z == 1 ? (O = U, Y = ee, J = _, se = W) : z == 2 ? (O = U, Y = _, J = ee, se = W) : (O = U, Y = _, J = W, se = ee);
      let le = t.sh0.codebook[h[S * 4 + 0]], he = t.sh0.codebook[h[S * 4 + 1]], Me = t.sh0.codebook[h[S * 4 + 2]], re = h[S * 4 + 3];
      D[P * 8 + 0] = Q, D[P * 8 + 1] = I, D[P * 8 + 2] = B, D[P * 8 + 3] = b, D[P * 8 + 4] = T, D[P * 8 + 5] = L, M[P * 32 + 24] = Z((0.5 + Ye * le) * 255), M[P * 32 + 25] = Z((0.5 + Ye * he) * 255), M[P * 32 + 26] = Z((0.5 + Ye * Me) * 255), M[P * 32 + 27] = re, M[P * 32 + 28] = Z(O * 128 + 128), M[P * 32 + 29] = Z(Y * 128 + 128), M[P * 32 + 30] = Z(J * 128 + 128), M[P * 32 + 31] = Z(se * 128 + 128), P++;
    }
    if (o > 0) {
      const S = new Uint32Array(2);
      S[0] = R, S[1] = Oe;
      const P = new Uint8Array(8 + R * 24);
      P.set(new Uint8Array(S.buffer), 0);
      const G = new Uint32Array(2);
      G[0] = R, G[1] = wt;
      const C = new Uint8Array(8 + R * 21);
      C.set(new Uint8Array(G.buffer), 0);
      for (let I = v, B = 0; I < x; I++) {
        const b = y[I * 4 + 0] + (y[I * 4 + 1] << 8), T = (b & 63) * 15, U = (b >> 6) * m + T, _ = new Uint8Array(9), W = new Uint8Array(15), ee = new Uint8Array(21);
        let z;
        for (let O = 0; O < 3; O++) {
          for (let Y = 0; Y < 3; Y++)
            z = t.shN.codebook[p[(U + Y) * 4 + O]], _[Y * 3 + O] = Z(Math.round(z * 128) + 128);
          for (let Y = 0; Y < 5; Y++)
            z = t.shN.codebook[p[(U + 3 + Y) * 4 + O]], W[Y * 3 + O] = Z(Math.round(z * 128) + 128);
          for (let Y = 0; Y < 7; Y++)
            z = t.shN.codebook[p[(U + 8 + Y) * 4 + O]], ee[Y * 3 + O] = Z(Math.round(z * 128) + 128);
        }
        P.set(_, 8 + B * 24), P.set(W, 8 + B * 24 + 9), C.set(ee, 8 + B * 21), B++;
      }
      const E = await Se(P);
      n.sh12Data.push(E.datas);
      const Q = await Se(C);
      n.sh3Data.push(Q.datas);
    }
    return await nn(M, R);
  }
  function u(v, x) {
    const R = x.byteLength / X, M = new Float32Array(x.buffer);
    for (let S = 0, P = 0, G = 0, C = 0; S < R; S++)
      P = M[S * 8], G = M[S * 8 + 1], C = M[S * 8 + 2], v.minX = Math.min(v.minX, P), v.maxX = Math.max(v.maxX, P), v.minY = Math.min(v.minY, G), v.maxY = Math.max(v.maxY, G), v.minZ = Math.min(v.minZ, C), v.maxZ = Math.max(v.maxZ, C);
    const D = 0;
    v.currentRadius = Math.sqrt(v.maxX * v.maxX + D * D + v.maxZ * v.maxZ), v.aabbCenter = new k((v.minX + v.maxX) / 2, (v.minY + v.maxY) / 2, (v.minZ + v.maxZ) / 2), v.maxRadius = 0.5 * Math.sqrt(Math.pow(v.maxX - v.minX, 2) + Math.pow(v.maxY - v.minY, 2) + Math.pow(v.maxZ - v.minZ, 2)), v.metaMatrix && v.aabbCenter.applyMatrix4(v.metaMatrix);
  }
}
function Rl(n) {
  const e = (E, Q, I) => n.on(E, Q, I), t = (E, ...Q) => n.fire(E, ...Q);
  let s, i = Date.now() + 36e5, a = 0, o = 0, r = 0, l = !1, A = null, c, d = { index: 0, version: 0 }, h = { index: 1, version: 0 }, p = !1;
  const m = t(Ee);
  let y = !1, g = 0;
  e(qa, () => c?.aabbCenter || new k());
  let f;
  const u = new Promise((E) => f = E);
  e(pt, async () => {
    const E = t(F);
    let Q = Ce ? E.maxRenderCountOfMobile : E.maxRenderCountOfPc;
    if (!E.bigSceneMode) {
      let I = await u;
      Q = Math.min(I, Q) + 10240;
    }
    return Q;
  }), e(Ot, async (E) => {
    const Q = t(F);
    if (Q.bigSceneMode) return 1;
    let I = Ce ? Q.maxRenderCountOfMobile : Q.maxRenderCountOfPc, B = await u;
    if (I = Math.min(B, I), !c.dataShDegree) return 1;
    if (E >= 3) {
      if (c.dataShDegree < 3) return 1;
    } else if (E >= 1) {
      if (c.dataShDegree < 1) return 1;
    } else
      return 1;
    const b = 1024 * 2;
    return Math.ceil(I / b);
  }), e(Di, async () => t(F).bigSceneMode ? 0 : (await u, c.dataShDegree)), e(Bi, async (E, Q = !0) => {
    try {
      await u;
      const I = !!c.header?.Flag2;
      A = await t(Va, E, Q, I), c && (c.textWatermarkVersion = Date.now());
    } catch {
      console.info("failed to generate watermark");
    }
  }), e(Ja, (E) => {
    m && (E ? (!h.active && (h.activeTime = Date.now()), h.active = !0) : (!d.active && (d.activeTime = Date.now()), d.active = !0));
  }), e(qs, () => {
    if (m) return d.version <= h.version ? d.xyz : h.xyz;
    if (c?.status === N.FetchDone || c?.status === N.FetchAborted) {
      if (c.activePoints && c.activePoints.length === void 0) return c.activePoints;
      const E = {}, Q = d.xyz;
      for (let I = 0, B = Q.length / 3, b = 0, T = 0, L = 0, U = ""; I < B; I++)
        b = Q[I * 3], T = Q[I * 3 + 1], L = Q[I * 3 + 2], U = `${Math.floor(b / 2) * 2 + 1},${Math.floor(T / 2) * 2 + 1},${Math.floor(L / 2) * 2 + 1}`, (E[U] = E[U] || []).push(b, T, L);
      return c.activePoints = E;
    }
    return d.xyz;
  });
  async function v(E) {
    if (s) return;
    if (c && (c.status === N.Invalid || c.status === N.FetchFailed))
      return t(F).viewerEvents?.fire(me), t(Jt, 0) || t(ce, { renderSplatCount: 0, visibleSplatCount: 0, modelSplatCount: 0 });
    if (!c || !c.downloadSize) return;
    const Q = t(F), I = c.status !== N.FetchReady && c.status !== N.Fetching;
    if (S(I), Q.disableStreamLoading && !I) {
      t(Xn, 100 * c.downloadSize / c.fileSize);
      return;
    }
    if (I) {
      const B = Math.min(c.fetchLimit, c.downloadSplatCount);
      !c.notifyFetchStopDone && (c.notifyFetchStopDone = !0) && t(Jt, B), r || (r = Date.now(), Q.onPerformanceUpdate?.({
        fullLoadTime: r - a,
        totalSplatCount: c.downloadSplatCount,
        phase: "complete"
      }));
    } else {
      const B = 100 * c.downloadSize / c.fileSize;
      t(Xn, B), Q.onPerformanceUpdate?.({
        currentSplatCount: c.downloadSplatCount,
        loadProgress: B,
        phase: "loading"
      });
    }
    c.dataSplatCount && (p || (p = !0, setTimeout(async () => {
      E ? await R(I) : await x(I), p = !1;
    })));
  }
  async function x(E) {
    if (s) return;
    const Q = d, I = await t(pt), B = A;
    let b = c.dataSplatCount, T = E ? c.watermarkCount : 0, L = c.meta.showWatermark && E ? (B?.byteLength || 0) / 32 : 0;
    if (c.renderSplatCount = b + T + L, c.renderSplatCount >= I && (c.renderSplatCount = I, T = 0, L = 0, b > I && (b = I)), t(ce, { visibleSplatCount: c.renderSplatCount, modelSplatCount: c.modelSplatCount + L }), Date.now() - Q.textureReadyTime < Bc || c.smallSceneUploadDone && c.lastTextWatermarkVersion == c.textWatermarkVersion) return;
    if (!Q.version) {
      t(va, (c.header?.Flag2 ? c.header.MaxTopY : c.header?.MinTopY) || 0);
      const J = c.aabbCenter || new k();
      t(Ma, J.x, J.y, J.z);
      let se = c.opts.format, le = "　";
      c.opts.format == "spx" ? (se = "spx v" + c.header.Version + (c.header.ExclusiveId ? (", " + c.header.ExclusiveId).substring(0, 5) : ""), le += c.CompressionRatio) : c.opts.format == "spz" ? (se = "spz v" + c.spzVersion, le += c.CompressionRatio) : c.opts.format == "sog" ? (se = "sog v" + c.sogVersion, le += c.CompressionRatio) : c.opts.format == "splat" && (le += c.CompressionRatio);
      const he = "　" + (c.fileSize / 1024 / 1024).toFixed(1) + "M";
      t(ce, { scene: `small (${se}) ${le}${he}` });
    }
    c.lastTextWatermarkVersion = c.textWatermarkVersion, Q.textureReady = !1;
    const U = 1024 * 2, _ = Math.ceil(2 * I / U), W = new Uint32Array(U * _ * 4), ee = new Float32Array(W.buffer), z = new Uint8Array(W.buffer);
    z.set(c.splatData.slice(0, b * 32), 0), T && z.set(c.watermarkData.slice(0, T * 32), b * 32), L && z.set(B.slice(0, L * 32), (b + T) * 32);
    const O = new Float32Array(c.renderSplatCount * 3);
    for (let J = 0, se = 0; J < c.renderSplatCount; J++)
      O[J * 3] = ee[J * 8], O[J * 3 + 1] = ee[J * 8 + 1], O[J * 3 + 2] = ee[J * 8 + 2];
    const Y = Date.now();
    if (Q.version = Y, Q.txdata = W, Q.xyz = O, Q.renderSplatCount = c.renderSplatCount, Q.visibleSplatCount = c.downloadSplatCount + L, Q.modelSplatCount = c.downloadSplatCount + L, Q.watermarkCount = T + L, Q.minX = c.minX, Q.maxX = c.maxX, Q.minY = c.minY, Q.maxY = c.maxY, Q.minZ = c.minZ, Q.maxZ = c.maxZ, c.meta.particleMode && !g && (g = performance.now(), t(ts, g), t(ft, !1), t(ns, 1)), c.maxRadius && t(tr, c.maxRadius), !l && Q.renderSplatCount > 0 && (l = !0, o = Date.now(), t(F).onPerformanceUpdate?.({
      firstFrameTime: o - a,
      currentSplatCount: Q.renderSplatCount,
      phase: "firstFrame"
    })), t(si, Q, c.currentRadius, c.currentRadius), i = Y, E && !c.smallSceneUploadDone) {
      c.smallSceneUploadDone = !0, t(Ka, c.sh12Data), t(Za, c.sh3Data), c.sh12Data = null, c.sh3Data = null;
      const J = t(F);
      t(Gn, J.shDegree === void 0 ? 3 : J.shDegree), t(qs), c.meta.particleMode && setTimeout(() => {
        t(ts, performance.now() + 5e3), t(ns, 2), setTimeout(() => t(Nn, !0), 5e3);
      }, Math.max(5e3 + g - performance.now(), 0));
    }
    t(ce, { renderSplatCount: c.renderSplatCount });
  }
  async function R(E) {
    if (s) return;
    const Q = await t(pt), I = 1024 * 2, B = Math.ceil(2 * Q / I), b = A, T = 0, L = (b?.byteLength || 0) / 32, U = Q - T - L;
    t(ce, { modelSplatCount: c.downloadSplatCount + L });
    let _ = d.version <= h.version ? d : h;
    if (d.version && (!_.index && !h.active || _.index && !d.active) || Date.now() - _.activeTime < Dc) return;
    if (E) {
      const te = t(F).viewerEvents;
      if (te && !te.fire(bi)) return;
    }
    if (!_.version) {
      let te = c.opts.format, ie = "　";
      c.opts.format == "spx" && (te = "spx" + (c.header.ExclusiveId ? (" " + c.header.ExclusiveId).substring(0, 6) : ""), ie += c.CompressionRatio);
      const Fe = "　" + (c.fileSize / 1024 / 1024).toFixed(1) + "M";
      t(ce, { scene: `large (${te}) ${ie}${Fe}` });
    }
    const W = Date.now();
    _.version = W, _.active = !1;
    let ee = 0;
    const z = [], O = t(wa), Y = t(Ue), J = t(Ve);
    for (const te of c.map.values())
      D(O, Y, J, te) && (z.push(te), te.currentRenderCnt = te.splatCount, ee += te.splatCount);
    t(ce, { cuts: `${z.length} / ${c.map.size}` });
    const se = Math.min(U / ee, 1);
    if (se > 0.95)
      for (const te of z) te.currentRenderCnt = te.splatCount * se | 0;
    else {
      z.sort((ie, Fe) => ie.distance - Fe.distance);
      for (const ie of z)
        ie.distance < 5 ? ie.distance *= 0.5 : ie.distance < 4 ? ie.distance *= 0.4 : ie.distance < 3 ? ie.distance *= 0.3 : ie.distance < 2 && (ie.distance *= 0.1);
      M(z, U);
      let te = 0;
      for (let ie of z) te += ie.currentRenderCnt;
      if (te > U) {
        let ie = te - U;
        for (let Fe = z.length - 1; Fe >= 0 && !(ie <= 0); Fe--) {
          const xe = z[Fe];
          xe.currentRenderCnt >= ie ? (xe.currentRenderCnt -= ie, ie = 0) : (ie -= xe.currentRenderCnt, xe.currentRenderCnt = 0);
        }
      } else if (te < U) {
        let ie = U - te;
        for (let Fe = 0; Fe < z.length && !(ie <= 0); Fe++) {
          let xe = z[Fe];
          if (xe.splatCount > xe.currentRenderCnt)
            if (xe.splatCount - xe.currentRenderCnt >= ie)
              xe.currentRenderCnt += ie, ie = 0;
            else {
              const Ni = xe.splatCount - xe.currentRenderCnt;
              xe.currentRenderCnt += Ni, ie -= Ni;
            }
        }
      }
    }
    const le = new Uint32Array(I * B * 4), he = new Float32Array(le.buffer), Me = new Uint8Array(le.buffer);
    let re = 0;
    for (let te of z)
      Me.set(te.splatData.slice(0, te.currentRenderCnt * 32), re * 32), re += te.currentRenderCnt;
    L && Me.set(b.slice(0, L * 32), (re + T) * 32);
    const Et = re + T + L, At = new Float32Array(Et * 3);
    for (let te = 0, ie = 0; te < Et; te++)
      At[te * 3] = he[te * 8], At[te * 3 + 1] = he[te * 8 + 1], At[te * 3 + 2] = he[te * 8 + 2];
    _.txdata = le, _.xyz = At, _.renderSplatCount = Et, _.visibleSplatCount = ee + c.watermarkCount + L, _.modelSplatCount = c.downloadSplatCount + L, _.watermarkCount = T + L, _.minX = c.header.MinX, _.maxX = c.header.MaxX, _.minY = c.header.MinY, _.maxY = c.header.MaxY, _.minZ = c.header.MinZ, _.maxZ = c.header.MaxZ, t(si, _), i = W, t(ce, { visibleSplatCount: _.visibleSplatCount, modelSplatCount: _.modelSplatCount });
  }
  function M(E, Q) {
    const I = E.map((T) => 1 / (T.distance + 1e-6)), B = I.reduce((T, L) => T + L, 0);
    let b = 0;
    if (E.forEach((T, L) => {
      T.currentRenderCnt = Math.min(Math.floor(I[L] / B * Q), T.splatCount), b += T.currentRenderCnt;
    }), b < Q) {
      const T = Q - b, L = E.map((_, W) => _.currentRenderCnt < _.splatCount ? I[W] : 0), U = L.reduce((_, W) => _ + W, 0);
      E.forEach((_, W) => {
        if (U > 0 && _.currentRenderCnt < _.splatCount) {
          const ee = Math.min(
            Math.floor(L[W] / U * T),
            _.splatCount - _.currentRenderCnt
          );
          _.currentRenderCnt += ee;
        }
      });
    }
  }
  function D(E, Q, I, B) {
    if (B.distance = Math.max(B.center.distanceTo(Q) - B.radius, 0), !B.distance || B.center.distanceTo(I) <= 2 * B.radius) return !0;
    const b = new Xe(B.center.x, B.center.y, B.center.z, 1).applyMatrix4(E), T = 3 * b.w;
    return !(b.z < -T || b.x < -T || b.x > T || b.y < -T || b.y > T);
  }
  function S(E) {
    if (!y) {
      if (c.header) {
        y = !0;
        const Q = new k(c.header.MinX, c.header.MinY, c.header.MinZ), I = new k(c.header.MaxX, c.header.MaxY, c.header.MaxZ);
        t(ii, Q.x, Q.y, Q.z, I.x, I.y, I.z, c.meta.showBoundBox);
      } else if (E) {
        y = !0;
        const Q = new k(c.minX, c.minY, c.minZ), I = new k(c.maxX, c.maxY, c.maxZ);
        t(ii, Q.x, Q.y, Q.z, I.x, I.y, I.z, c.meta.showBoundBox);
      }
    }
  }
  function P() {
    s || (s = !0, c?.abortController?.abort(), c?.map?.clear(), c = null, A = null, d = null, h = null);
  }
  function G(E) {
    if (E.opts.format === "spx")
      xl(E);
    else if (E.opts.format === "splat")
      vl(E);
    else if (E.opts.format === "ply")
      Sl(E);
    else if (E.opts.format === "spz")
      Dl(E);
    else if (E.opts.format === "sog")
      Tl(E);
    else
      return !1;
    return !0;
  }
  function C(E, Q) {
    if (s) return;
    const I = t(F), B = Ce ? I.maxRenderCountOfMobile : I.maxRenderCountOfPc, b = t(Ee);
    if (a = Date.now(), o = 0, r = 0, l = !1, E.fetchReload = hl(Q.updateDate || 0), c = new xc(E, Q), b && Q.autoCut) {
      const L = Q.pcDownloadLimitSplatCount || Lc, U = Q.mobileDownloadLimitSplatCount || Rc, _ = Ce ? U : L;
      c.fetchLimit = Math.min(Q.autoCut * Q.autoCut * B + B, _);
    } else
      c.fetchLimit = B;
    const T = () => {
      if (!c || c.status == N.Invalid || c.status == N.FetchFailed)
        return f(0);
      c.modelSplatCount > 0 ? (f(c.modelSplatCount), !c.meta.particleMode && c.dataSplatCount ? setTimeout(() => t(Fa), 5) : setTimeout(T, 10)) : setTimeout(T, 10);
    };
    if (T(), !G(c)) {
      console.error("Unsupported format:", E.format), t(Jt, 0);
      return;
    }
    t(Si), t(ce, { cuts: "" });
  }
  e(ka, (E, Q) => C(E, Q)), e(Ta, (E = 1e4) => Date.now() - i < E), e(Ra, () => P()), t(
    Rt,
    async () => await v(m),
    () => !s
  );
}
var hs = /* @__PURE__ */ ((n) => (n[n.ModelCenterCirccle = 1] = "ModelCenterCirccle", n[n.ScreenCenterCircle = 2] = "ScreenCenterCircle", n[n.ScreenMiddleToLeftRight = 3] = "ScreenMiddleToLeftRight", n[n.ScreenMiddleToTopBottom = 4] = "ScreenMiddleToTopBottom", n))(hs || {});
const fo = "currentVisibleRadius", po = "currentLightRadius", mo = "transitionEffect", Ls = "splatShTexture12", Co = "maxPixelDiameter", yo = "minPixelDiameter", Fs = "activeFlagValue", Ps = "splatShTexture3", Io = "performanceNow", Mn = "performanceAct", Ll = "waterMarkColor", wo = "showWaterMark", Eo = "useSimilarExp", _s = "splatTexture0", Us = "splatTexture1", bo = "particleMode", So = "bigSceneMode", vo = "lightFactor", Mo = "debugEffect", xo = "usingIndex", Fl = "splatIndex", Qo = "maxRadius", Bo = "flagValue", Do = "pointMode", ko = "markPoint", To = "shDegree", Ro = "viewport", Lo = "minAlpha", Fo = "focal", Po = "topY", _o = "aabbCenter", Uo = "lookAtCenter";
let K = 0;
K++;
const No = `$${K++}`, Pl = `$${K++}`;
K++;
K++;
K++;
const Wo = `$${K++}`, _l = `$${K++}`, Ho = `$${K++}`, Ul = `$${K++}`, Nl = `$${K++}`, Wl = `$${K++}`, Hl = `$${K++}`, Yl = `$${K++}`;
K++;
const Gl = `$${K++}`, zl = `$${K++}`;
K++;
const Vl = `$${K++}`;
K++;
const Ol = `$${K++}`;
K++;
const Xl = `$${K++}`, Jl = `$${K++}`, Kl = `$${K++}`, Zl = `$${K++}`, jl = `$${K++}`, ql = `$${K++}`, $l = `$${K++}`, eA = `$${K++}`, tA = `$${K++}`, nA = `$${K++}`, Yo = `$${K++}`, sA = `$${K++}`, iA = `$${K++}`, hi = `$${K++}`, Go = `$${K++}`, zo = `$${K++}`;
var oA = `precision highp float;
precision highp int;

uniform highp usampler2D splatTexture0, splatTexture1, splatShTexture12, splatShTexture3;
uniform vec2 focal, viewport;
uniform int usingIndex, shDegree, particleMode, transitionEffect;
uniform bool pointMode, bigSceneMode, showWaterMark, debugEffect;
uniform float topY, maxRadius, currentVisibleRadius, currentLightRadius, performanceNow, performanceAct;
uniform float minPixelDiameter, maxPixelDiameter, minAlpha;
uniform vec3 aabbCenter, lookAtCenter;
uniform vec4 markPoint, waterMarkColor;
uniform uint flagValue, activeFlagValue;

attribute uint splatIndex;

varying vec4 vColor;
varying vec3 vPosition;
vec3 animateParticle(vec3 v3Cen) {
    if (particleMode < 1)
        return v3Cen;
    float factor = particleMode > 1 ? ((performanceAct - performanceNow) / 5000.0) : ((performanceNow - performanceAct) / 5000.0);
    float radius = particleMode > 1 ? (max(currentVisibleRadius, maxRadius) * 0.6 * min((performanceNow) / 3000.0, 1.0)) : (max(currentVisibleRadius, maxRadius) * 0.6 * min((performanceNow - performanceAct) / 3000.0, 1.0));
    if (factor <= 0.0)
        return v3Cen;

    
    vec3 randSeed = fract(sin(vec3(dot(v3Cen, vec3(12.9898, 78.233, 37.719)), dot(v3Cen.yzx, vec3(49.123, 23.456, 87.654)), dot(v3Cen.zxy, vec3(34.567, 91.234, 56.789))))) * 2.0 - 1.0;

    
    float phase = factor * 12.0 + v3Cen.y * (15.0 + randSeed.x * 3.0) + v3Cen.z * (13.0 + randSeed.y * 2.0);

    
    float wave1 = sin(phase * (2.0 + randSeed.y * 1.5 + randSeed.z * 1.5));
    float wave2 = cos(phase * (1.2 + randSeed.x * 0.3) + v3Cen.x * 20.0);
    float dynamicFactor = mix(wave1, wave2, 0.5 + randSeed.z * 0.2) * 0.5 + 0.5;

    
    float amplitude = radius * 0.25 * factor * (0.9 + randSeed.z * 0.2);

    
    vec3 offset = vec3(amplitude * (dynamicFactor * 2.0 - 1.0), amplitude * randSeed.x * 5.0, amplitude * randSeed.y * 2.5);

    
    vec3 newPos = v3Cen + offset;
    float newDist = length(newPos);
    if (newDist > radius) {
        vec3 dir = normalize(newPos);
        float penetration = newDist - radius;
        float elasticity = 0.7 + randSeed.z * 0.2;

        
        vec3 bounceVec = dir * penetration * elasticity;
        vec3 tangent = normalize(cross(dir, vec3(randSeed.x, randSeed.y, 1.0)));
        newPos -= bounceVec - tangent * (length(randSeed.xy) * penetration * 0.2);
    }

    
    return normalize(newPos) * min(length(newPos), radius);
}
const float FactorSH = 0.0625;
const uint MaskSH = 0x1Fu;
const float SH_C1 = 0.4886025119029199;
const float[5] SH_C2 = float[](1.0925484305920792, -1.0925484305920792, 0.31539156525252005, -1.0925484305920792, 0.5462742152960396);
const float[7] SH_C3 = float[](-0.5900435899266435, 2.890611442640554, -0.4570457994644658, 0.3731763325901154, -0.4570457994644658, 1.445305721320277, -0.5900435899266435);

vec3[15] splatReadShDatas() {
    int shCnt = 0;
    float[45] fSHs;
    uvec4 rgb12 = texelFetch(splatShTexture12, ivec2((splatIndex & 0x7ffu), (splatIndex >> 11)), 0);
    if (rgb12.a > 0u) {
        shCnt = 3;
        fSHs[0] = float((rgb12.r >> 27) & MaskSH) * FactorSH - 1.0;
        fSHs[1] = float((rgb12.r >> 22) & MaskSH) * FactorSH - 1.0;
        fSHs[2] = float((rgb12.r >> 17) & MaskSH) * FactorSH - 1.0;
        fSHs[3] = float((rgb12.r >> 12) & MaskSH) * FactorSH - 1.0;
        fSHs[4] = float((rgb12.r >> 7) & MaskSH) * FactorSH - 1.0;
        fSHs[5] = float((rgb12.r >> 2) & MaskSH) * FactorSH - 1.0;
        fSHs[6] = float(((rgb12.r << 3) | (rgb12.g >> 29)) & MaskSH) * FactorSH - 1.0;
        fSHs[7] = float((rgb12.g >> 24) & MaskSH) * FactorSH - 1.0;
        fSHs[8] = float((rgb12.g >> 19) & MaskSH) * FactorSH - 1.0;

        if (shDegree > 1) {
            shCnt = 8;
            fSHs[9] = float((rgb12.g >> 14) & MaskSH) * FactorSH - 1.0;
            fSHs[10] = float((rgb12.g >> 9) & MaskSH) * FactorSH - 1.0;
            fSHs[11] = float((rgb12.g >> 4) & MaskSH) * FactorSH - 1.0;
            fSHs[12] = float(((rgb12.g << 1) | (rgb12.b >> 31)) & MaskSH) * FactorSH - 1.0;
            fSHs[13] = float((rgb12.b >> 26) & MaskSH) * FactorSH - 1.0;
            fSHs[14] = float((rgb12.b >> 21) & MaskSH) * FactorSH - 1.0;
            fSHs[15] = float((rgb12.b >> 16) & MaskSH) * FactorSH - 1.0;
            fSHs[16] = float((rgb12.b >> 11) & MaskSH) * FactorSH - 1.0;
            fSHs[17] = float((rgb12.b >> 6) & MaskSH) * FactorSH - 1.0;
            fSHs[18] = float((rgb12.b >> 1) & MaskSH) * FactorSH - 1.0;
            fSHs[19] = float(((rgb12.b << 4) | (rgb12.a >> 28)) & MaskSH) * FactorSH - 1.0;
            fSHs[20] = float(((rgb12.a >> 23) & MaskSH)) * FactorSH - 1.0;
            fSHs[21] = float((rgb12.a >> 18) & MaskSH) * FactorSH - 1.0;
            fSHs[22] = float((rgb12.a >> 13) & MaskSH) * FactorSH - 1.0;
            fSHs[23] = float((rgb12.a >> 8) & MaskSH) * FactorSH - 1.0;

            if (shDegree > 2) {
                uvec4 rgb3 = texelFetch(splatShTexture3, ivec2(splatIndex & 0x7ffu, splatIndex >> 11), 0);
                if (rgb3.a > 0u) {
                    shCnt = 15;
                    fSHs[24] = float((rgb3.r >> 27) & MaskSH) * FactorSH - 1.0;
                    fSHs[25] = float((rgb3.r >> 22) & MaskSH) * FactorSH - 1.0;
                    fSHs[26] = float((rgb3.r >> 17) & MaskSH) * FactorSH - 1.0;
                    fSHs[27] = float((rgb3.r >> 12) & MaskSH) * FactorSH - 1.0;
                    fSHs[28] = float((rgb3.r >> 7) & MaskSH) * FactorSH - 1.0;
                    fSHs[29] = float((rgb3.r >> 2) & MaskSH) * FactorSH - 1.0;
                    fSHs[30] = float(((rgb3.r << 3) | (rgb3.g >> 29)) & MaskSH) * FactorSH - 1.0;
                    fSHs[31] = float((rgb3.g >> 24) & MaskSH) * FactorSH - 1.0;
                    fSHs[32] = float((rgb3.g >> 19) & MaskSH) * FactorSH - 1.0;
                    fSHs[33] = float((rgb3.g >> 14) & MaskSH) * FactorSH - 1.0;
                    fSHs[34] = float((rgb3.g >> 9) & MaskSH) * FactorSH - 1.0;
                    fSHs[35] = float((rgb3.g >> 4) & MaskSH) * FactorSH - 1.0;
                    fSHs[36] = float(((rgb3.g << 1) | (rgb3.b >> 31)) & MaskSH) * FactorSH - 1.0;
                    fSHs[37] = float((rgb3.b >> 26) & MaskSH) * FactorSH - 1.0;
                    fSHs[38] = float((rgb3.b >> 21) & MaskSH) * FactorSH - 1.0;
                    fSHs[39] = float((rgb3.b >> 16) & MaskSH) * FactorSH - 1.0;
                    fSHs[40] = float((rgb3.b >> 11) & MaskSH) * FactorSH - 1.0;
                    fSHs[41] = float((rgb3.b >> 6) & MaskSH) * FactorSH - 1.0;
                    fSHs[42] = float((rgb3.b >> 1) & MaskSH) * FactorSH - 1.0;
                    fSHs[43] = float(((rgb3.b << 4) | (rgb3.a >> 28)) & MaskSH) * FactorSH - 1.0;
                    fSHs[44] = float((rgb3.a >> 23) & MaskSH) * FactorSH - 1.0;
                }
            }
        }
    }

    vec3[15] sh;
    for (int i = 0; i < 15; ++i) {
        sh[i] = i < shCnt ? vec3(fSHs[i * 3], fSHs[i * 3 + 1], fSHs[i * 3 + 2]) : vec3(0.0);
    }
    return sh;
}

vec3 splatEvalSH(in vec3 v3Cen) {
    vec3 dir = normalize(v3Cen - cameraPosition);
    float x = dir.x;
    float y = dir.y;
    float z = dir.z;

    vec3[15] sh = splatReadShDatas();
    vec3 result = SH_C1 * (-sh[0] * y + sh[1] * z - sh[2] * x);

    if (shDegree > 1) {
        float xx = x * x;
        float yy = y * y;
        float zz = z * z;
        float xy = x * y;
        float yz = y * z;
        float xz = x * z;

        result += sh[3] * (SH_C2[0] * xy) +
            sh[4] * (SH_C2[1] * yz) +
            sh[5] * (SH_C2[2] * (2.0 * zz - xx - yy)) +
            sh[6] * (SH_C2[3] * xz) +
            sh[7] * (SH_C2[4] * (xx - yy));

        if (shDegree > 2) {
            result += sh[8] * (SH_C3[0] * y * (3.0 * xx - yy)) +
                sh[9] * (SH_C3[1] * xy * z) +
                sh[10] * (SH_C3[2] * y * (4.0 * zz - xx - yy)) +
                sh[11] * (SH_C3[3] * z * (2.0 * zz - 3.0 * xx - 3.0 * yy)) +
                sh[12] * (SH_C3[4] * x * (4.0 * zz - xx - yy)) +
                sh[13] * (SH_C3[5] * z * (xx - yy)) +
                sh[14] * (SH_C3[6] * x * (xx - 3.0 * yy));
        }
    }
    return result;
}

void main() {
    uvec4 cen, cov3d;
    if (bigSceneMode) {
        if (usingIndex == 0) {
            cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
            cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
        } else {
            cen = texelFetch(splatTexture1, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
            cov3d = texelFetch(splatTexture1, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
        }
    } else {
        cen = texelFetch(splatTexture0, ivec2((splatIndex & 0x3ffu) << 1, splatIndex >> 10), 0);
        cov3d = texelFetch(splatTexture0, ivec2(((splatIndex & 0x3ffu) << 1) | 1u, splatIndex >> 10), 0);
    }

    bool isWatermark = (cen.w & 65536u) > 0u;
    float colorA = (float(cov3d.w >> 24) / 255.0);
    if (colorA < minAlpha && !isWatermark) {
        vColor = vec4(0.0);
        return;
    }

    vec3 v3Cen = uintBitsToFloat(cen.xyz);
    v3Cen = animateParticle(v3Cen);
    v3Cen = WatermarkEffect(v3Cen, isWatermark, debugEffect, performanceNow);

    vec4 cam = modelViewMatrix * vec4(v3Cen, 1.0);
    vec4 pos2d = projectionMatrix * cam;
    float clip = 1.05 * pos2d.w;
    if (pos2d.z < -clip || pos2d.x < -clip || pos2d.x > clip || pos2d.y < -clip || pos2d.y > clip || isWatermark && (!showWaterMark || pointMode)) {
        vColor = vec4(0.0);
        return;
    }

    
    float currentRadius = length(aabbCenter - v3Cen);
    if (currentVisibleRadius > 0.0 && currentRadius > currentVisibleRadius) {
        vColor = vec4(0.0);
        return;
    }

    vec2 uh1 = unpackHalf2x16(cov3d.x), uh2 = unpackHalf2x16(cov3d.y), uh3 = unpackHalf2x16(cov3d.z);
    mat3 Vrk = mat3(uh1.x, uh1.y, uh2.x, uh1.y, uh2.y, uh3.x, uh2.x, uh3.x, uh3.y);

    float ZxZ = cam.z * cam.z;
    mat3 J_m3 = mat3(focal.x / cam.z, 0.0, -(focal.x * cam.x) / ZxZ, 0.0, focal.y / cam.z, -(focal.y * cam.y) / ZxZ, 0.0, 0.0, 0.0);

    mat3 T_m3 = transpose(mat3(modelViewMatrix)) * J_m3;
    mat3 cov2d = transpose(T_m3) * Vrk * T_m3;

    cov2d[0][0] += 0.3;
    cov2d[1][1] += 0.3;
    vec3 cov2Dv = vec3(cov2d[0][0], cov2d[0][1], cov2d[1][1]);
    float disc = max(0.0, (cov2Dv.x + cov2Dv.z) * (cov2Dv.x + cov2Dv.z) / 4.0 - (cov2Dv.x * cov2Dv.z - cov2Dv.y * cov2Dv.y));
    float eigenValue1 = 0.5 * (cov2Dv.x + cov2Dv.z) + sqrt(disc);
    float eigenValue2 = max(0.5 * (cov2Dv.x + cov2Dv.z) - sqrt(disc), 0.0);
    float eigenValueOrig1 = eigenValue1;
    float eigenValueOrig2 = eigenValue2;

    
    if (!isWatermark) {
        if (pointMode) {
            eigenValue1 = eigenValue2 = 0.5;
        }

        if (!bigSceneMode && currentLightRadius > 0.0) {
            
            if (transitionEffect == 1) {
                
                vec3 transitionCenter = lookAtCenter;
                float distanceToCenter = length(v3Cen - transitionCenter);

                
                float fadeWidth = currentLightRadius * 0.1; 
                float fadeStart = currentLightRadius - fadeWidth;

                if (distanceToCenter < fadeStart) {
                    
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                } else if (distanceToCenter < currentLightRadius) {
                    
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                    
                }
                
            } else {
                vec4 p = projectionMatrix * (modelViewMatrix * vec4(v3Cen, 1.0));
                float currentRatio = transitionEffect == 2 ? length(p.xy / p.w) : (transitionEffect == 3 ? length(p.xx / p.w) : length(p.yy / p.w));
                float currentLightRatio = (performanceNow - performanceAct) / 500.0;
                if (currentRatio < currentLightRatio) {
                    if (pointMode) {
                        eigenValue1 = eigenValueOrig1;
                        eigenValue2 = eigenValueOrig2;
                    } else {
                        eigenValue1 = eigenValue2 = 0.5;
                    }
                }
            }
        }
    }

    vPosition = vec3(position.xy, -1.0);
    vec2 eigenVector1 = normalize(vec2(cov2Dv.y, eigenValue1 - cov2Dv.x));
    if (markPoint.w > 0.0 && length(vec3(markPoint.xyz) - v3Cen) < 0.000001) {
        vColor = vec4(1.0, 1.0, 0.0, 1.0);
        eigenValue1 = eigenValue2 = 11.0;
        eigenVector1 = normalize(vec2(11.0, 0.0));
        vPosition.z = 1.0; 
    } else if (isWatermark) {
        vColor = waterMarkColor;
    } else {
        vColor = vec4(float(cov3d.w & 0xFFu) / 255.0, float((cov3d.w >> 8) & 0xFFu) / 255.0, float((cov3d.w >> 16) & 0xFFu) / 255.0, colorA);
        if (shDegree > 0) {
            vColor.rgb += splatEvalSH(v3Cen);
        }
        vColor = FvEffect(cen, vColor, activeFlagValue, performanceNow);

        
        
    }

    float diameter1 = min(sqrt(2.0 * eigenValue1), maxPixelDiameter);
    float diameter2 = min(sqrt(2.0 * eigenValue2), maxPixelDiameter);
    if (diameter1 < minPixelDiameter && diameter2 < minPixelDiameter && (pointMode && currentRadius < currentLightRadius || !pointMode && currentRadius > currentLightRadius)) {
        vColor = vec4(0.0);
        return;
    }

    vec2 eigenVector2 = vec2(eigenVector1.y, -eigenVector1.x);
    vec2 majorAxis = eigenVector1 * diameter1;
    vec2 minorAxis = eigenVector2 * diameter2;

    vec2 v2Center = vec2(pos2d) / pos2d.w;  
    gl_Position = vec4(v2Center + vPosition.x * majorAxis / viewport + vPosition.y * minorAxis / viewport, 1.0, 1.0);
}`, aA = `precision highp float;

uniform float lightFactor;
uniform float minAlpha;
uniform bool useSimilarExp;
varying vec4 vColor;
varying vec3 vPosition;

void main() {
    float alpha = vColor.a;
    if (alpha < minAlpha) {
        gl_FragColor = vec4(0.0);
        return;
    }
    float r2 = dot(vPosition.xy, vPosition.xy);
    if (r2 > 4.0) {
        gl_FragColor = vec4(0.0);
        return;
    }

    if (vPosition.z >= 1.0) {
        alpha = 1.0;
    } else {
        alpha *= useSimilarExp ? (1.0 / (1.0 + r2 * (1.0 + 0.5 * r2))) : exp(-r2);
        if (alpha <= minAlpha) {
            gl_FragColor = vec4(0.0);
            return;
        }
    }

    gl_FragColor = vec4(lightFactor * vColor.rgb, alpha);
}`, rA = `float fnWave(float minVal, float maxVal, float time) {
    return (sin(time * 0.005) + 1.0) * 0.5 * (maxVal - minVal) + minVal;
}`, cA = `vec4 FvEffect(uvec4 uv4Cen, vec4 v4Color, uint activeFv, float time) {
    uint fvSplat = uv4Cen.w & 65535u;
    if (fvSplat > 0u && fvSplat == activeFv) {
        return vec4(v4Color.rgb * fnWave(1.0, 1.6, time), v4Color.a);
    }
    return v4Color;
}`, lA = `vec3 WatermarkEffect(vec3 v3Cen, bool isWatermark, bool enableEffect, float time) {
    if (isWatermark && enableEffect) {
        v3Cen.y += sin(time * 0.002 + v3Cen.x) * 0.1; 
    }
    return v3Cen;
}`;
function AA(n) {
  let e = !1;
  const t = (m, y, g) => n.on(m, y, g), s = (m, ...y) => n.fire(m, ...y);
  let i = 0, a = 0;
  const o = [];
  let r = 0, l = 0, A = 0;
  t(ja, () => A), t(Cs, () => {
  }, !0), t(ys, () => {
  }, !0), t(hn, () => {
  }, !0), t(gn, () => {
  }, !0), t(Xi, async () => {
    const m = new zs();
    m.setIndex([0, 1, 2, 0, 2, 3]);
    const y = new Float32Array(12), g = new xt(y, 3);
    m.setAttribute("position", g), g.setXYZ(0, -2, -2, 0), g.setXYZ(1, -2, 2, 0), g.setXYZ(2, 2, 2, 0), g.setXYZ(3, 2, -2, 0), g.needsUpdate = !0;
    let f = new zs().copy(m);
    const u = await s(pt);
    if (e) return;
    const v = new Uint32Array(u), x = new Rr(v, 1, !1);
    return x.setUsage(Lr), x.needsUpdate = !0, f.setAttribute(Fl, x), f.instanceCount = 0, t(Zi, (R, M, D, S, P) => {
      s(qi, M), v.set(R, 0), x.clearUpdateRanges(), x.addUpdateRange(0, P), x.needsUpdate = !0, x.onUpload(() => {
        s(Ja, M), s(ce, { renderSplatCount: P });
      }), f.instanceCount = P, s(ne), s(ce, { sortTime: `${D} / ${Date.now() - S}`, bucketBits: r, sortType: l });
    }), t(tc, () => f), t(
      Cs,
      () => {
        x.array = null, f.dispose();
      },
      !0
    ), f;
  }), t(Ji, async () => {
    const m = await s(pt);
    if (e) return;
    const y = 1024 * 2, g = Math.ceil(2 * m / y), f = s(F), u = new aa({
      uniforms: s(Oi),
      vertexShader: c(oA),
      fragmentShader: c(aA),
      transparent: !0,
      alphaTest: 1,
      blending: Fr,
      depthTest: f.depthTest !== !1,
      // 是否启用深度测试。深度测试用于确保只有离相机更近的物体才会被渲染
      depthWrite: !1,
      // 是否将深度值写入深度缓冲区
      side: et
    }), v = new Uint32Array(y * g * 4);
    let x = new dt(v, y, g, ut, ht);
    x.internalFormat = "RGBA32UI", x.needsUpdate = !0, u.uniforms[_s].value = x;
    const R = s(Ee) ? g : 1, M = new Uint32Array(y * R * 4);
    let D = new dt(M, y, R, ut, ht);
    D.internalFormat = "RGBA32UI", D.needsUpdate = !0, u.uniforms[Us].value = D;
    const S = await s(Ot, 1), P = new Uint32Array(y * S * 4);
    let G = new dt(P, y, S, ut, ht);
    G.internalFormat = "RGBA32UI", G.needsUpdate = !0, u.uniforms[Ls].value = G;
    const C = await s(Ot, 3), E = new Uint32Array(y * C * 4);
    let Q = new dt(E, y, C, ut, ht);
    Q.internalFormat = "RGBA32UI", Q.needsUpdate = !0, u.uniforms[Ps].value = Q, u.needsUpdate = !0;
    let I = !1;
    t(ji, (b) => {
      if (!s(Ee)) {
        if (I && !b.renderSplatCount) return;
        I = !b.renderSplatCount;
      }
      const T = b.txdata;
      b.txdata = null;
      const L = new dt(T, y, g, ut, ht);
      L.onUpdate = () => {
        b.textureReady = !0, b.textureReadyTime = Date.now(), p(b), s(_a, b.renderSplatCount);
      }, L.internalFormat = "RGBA32UI", L.needsUpdate = !0, b.index ? (u.uniforms[Us].value = L, D = L) : (u.uniforms[_s].value = L, x = L), u.needsUpdate = !0, s(ne);
    }), t(Ka, async (b) => {
      if (s(Ee) || !b || !b.length) return;
      const T = new Uint32Array(y * await s(Ot, 1) * 4), L = new Uint8Array(T.buffer);
      for (let _ = 0, W = 0; _ < b.length; _++)
        L.set(b[_], W), W += b[_].byteLength;
      const U = new dt(T, y, S, ut, ht);
      U.internalFormat = "RGBA32UI", U.needsUpdate = !0, u.uniforms[Ls].value = U, u.needsUpdate = !0, s(ne);
    }), t(Za, async (b) => {
      if (s(Ee) || !b || !b.length) return;
      const T = new Uint32Array(y * await s(Ot, 3) * 4), L = new Uint8Array(T.buffer);
      for (let _ = 0, W = 0; _ < b.length; _++)
        L.set(b[_], W), W += b[_].byteLength;
      const U = new dt(T, y, S, ut, ht);
      U.internalFormat = "RGBA32UI", U.needsUpdate = !0, u.uniforms[Ps].value = U, u.needsUpdate = !0, s(ne);
    }), t(Ki, () => u), t(
      hn,
      () => {
        const b = s($), { width: T, height: L } = s(It), U = Math.abs(b.projectionMatrix.elements[0]) * 0.5 * T, _ = Math.abs(b.projectionMatrix.elements[5]) * 0.5 * L, W = s(Ki);
        W.uniforms[Fo].value.set(U, _), W.uniformsNeedUpdate = !0, s(ne);
      },
      !0
    ), t(
      gn,
      () => {
        const { width: b, height: T } = s(It);
        u.uniforms[Ro].value.set(b, T), u.uniformsNeedUpdate = !0, s(ne);
      },
      !0
    ), t(qi, (b) => {
      u.uniforms[xo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(io, (b) => {
      u.uniforms[mo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(ft, (b) => {
      const T = s(F);
      b === void 0 && (b = !T.pointcloudMode), u.uniforms[Do].value = b, u.uniformsNeedUpdate = !0, T.pointcloudMode = b, s(ne), T.viewerEvents && (T.viewerEvents.fire(F).pointcloudMode = b);
    }), t($i, (b) => {
      u.uniforms[So].value = b, u.uniformsNeedUpdate = !0;
      const T = s(F);
      T.bigSceneMode = b, s(ne);
    }), t(jt, (b) => {
      u.uniforms[vo].value = b, u.uniformsNeedUpdate = !0;
      const T = s(F);
      T.lightFactor = b, s(ne);
    });
    let B = !1;
    return t(va, (b) => {
      s(Ee) || B || (B = !0, u.uniforms[Po].value = b, u.uniformsNeedUpdate = !0, s(ne));
    }), t(Ma, (b, T, L) => {
      u.uniforms[_o].value.set(b, T, L), u.uniformsNeedUpdate = !0, s(ne);
    }), t(xa, (b, T, L) => {
      u.uniforms[Uo].value.set(b, T, L), u.uniformsNeedUpdate = !0, s(ne);
    }), t(bt, (b) => {
      u.uniforms[fo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(Nt, (b) => {
      u.uniforms[po].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(tr, (b) => {
      u.uniforms[Qo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(Fn, (b, T, L, U) => {
      u.uniforms[ko].value = [b, T, L, U ? 1 : -1], u.uniformsNeedUpdate = !0, s(ne);
    }), t(Zs, (b = !0) => {
      u.uniforms[wo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(Qa, (b) => {
      u.uniforms[Io].value = b, u.uniformsNeedUpdate = !0;
    }), t(ts, (b) => {
      u.uniforms[Mn].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(Ss, (b, T) => {
      u.uniforms[yo].value = b, u.uniforms[Co].value = T, u.uniformsNeedUpdate = !0, s(ne);
    }), t(vs, (b) => {
      u.uniforms[Lo].value = Math.min(Math.max(0, b), 255) / 255, u.uniformsNeedUpdate = !0, s(ne);
    }), t(Ms, (b = !1) => {
      u.uniforms[Eo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(ns, (b) => {
      u.uniforms[bo].value = b, u.uniformsNeedUpdate = !0, s(ne);
    }), t(nc, (b) => {
      u.uniforms[Mo].value = b, u.uniformsNeedUpdate = !0;
    }), t(Gn, async (b) => {
      if (s(Ee)) return;
      const T = await s(Di);
      b < 0 && (b = 0), b > T && (b = T), A = b, u.uniforms[To].value = b, u.uniformsNeedUpdate = !0, s(ce, { shDegree: `${b} / max ${T}` }), s(ne);
    }), t(dc, (b = 0, T = 0) => {
      u.uniforms[Bo].value = b << 16 | T, u.uniforms[Mn].value = performance.now(), u.uniformsNeedUpdate = !0, s(ne);
    }), t(uc, (b = -1) => {
      if (b < 0) return u.uniforms[Fs].value;
      u.uniforms[Fs].value = b, u.uniforms[Mn].value = performance.now(), u.uniformsNeedUpdate = !0, s(ne);
    }), t(
      ys,
      () => {
        u.dispose(), x && x.dispose(), D && D.dispose(), G && G.dispose(), Q && Q.dispose();
      },
      !0
    ), u;
  });
  function c(m) {
    return Yt.cmn = rA.trim(), Yt.FvEffect = (Yt["custom-FvEffect"] || cA).trim(), Yt.WatermarkEffect = (Yt["custom-WatermarkEffect"] || lA).trim(), `#include <cmn>
#include <FvEffect>
#include <WatermarkEffect>
` + m;
  }
  t(Sa, async () => {
    const m = new Ie(await s(Xi), await s(Ji));
    return s(hn), s(gn), s($i, s(Ee)), s(ft, s(Da)), m;
  });
  function d() {
    s(hn), s(gn);
  }
  window.addEventListener("resize", d), t(La, () => {
    e = !0, window.removeEventListener("resize", d), s(Cs), s(ys);
  }), t(oi, () => {
    if (!s(nr)) return;
    const m = s(F), y = s(ve)?.meta || {}, g = m.qualityLevel || Be.Default5;
    if (s(Gn, [0, 1, 2, 3, 3, 3, 3, 3, 3][g - 1]), Ce) {
      const u = [4, 3, 3, 2, 2, 2, 1, 1, 1], v = [7, 6, 5, 4, 4, 3, 2, 2, 2], x = y.minPixelDiameter || u[g - 1], R = y.maxPixelDiameter || (g < Be.L4 ? 128 : g > Be.L6 ? 512 : 256);
      s(Ss, x, R), s(vs, y.minAlpha || v[g - 1]), s(Ms, !1);
    } else {
      const u = [128, 256, 256, 512, 512, 1024, 1024, 1024, 1024], v = [5, 4, 3, 2, 2, 1, 1, 1, 1], x = y.minPixelDiameter || (g < Be.L4 ? 2 : 1), R = y.maxPixelDiameter || u[g - 1];
      s(Ss, x, R), s(vs, y.minAlpha || v[g - 1]), s(Ms, g > Be.L6);
    }
    s(Ti);
  }), t(Fa, async () => {
    if (s(Ee)) return;
    const m = s(F);
    if (m.disableTransitionEffectOnLoad) return s(bt, 0);
    if (m.disableProgressiveLoading) {
      s(bt, 0), s(Pn, !0);
      return;
    }
    let y = Date.now(), g = m.transitionAnimDuration || 4e3, f = 0, u = !1, v = 0;
    const x = (R) => (R = Math.max(0, Math.min(1, R)), R * R * (3 - 2 * R));
    s(bt, f), s(
      Rt,
      () => {
        if (e) return;
        const R = Date.now(), M = R - y;
        let D = Math.min(M / g, 1);
        if (s(js) && !v && (v = R), v) {
          const G = R - v;
          D = Math.min(M / g, 1), (D > 0.95 || G > 2e3) && (D = 1);
        }
        f = x(D) * a, s(bt, f), D >= 1 && (s(Pn, !0), s(bt, 0), u = !0);
      },
      () => !e && !u,
      3
    );
  }), t(Pn, (m = !1) => {
    if (s(Ee)) return;
    const y = s(F);
    if (s(io, y.transitionEffect), y.transitionEffect === hs.ModelCenterCirccle) {
      for (; o.length; ) o.pop().stop = !0;
      const g = Date.now(), f = 3e3, u = () => {
        const x = s(js), R = s(Pa), M = s(Ua) || 0, D = Date.now() - g, S = s(pt) || 0;
        y.pointcloudMode, M >= S, !R && (x || // 原始判断
        M > 0 && D > 1e3 || // 有纹理数据且等待超过1秒
        D > f) ? (D > f ? console.warn("等待数据超时，强制启动过渡动画") : !x && M > 0 && console.warn("数据状态检查异常，但有纹理数据，启动过渡动画"), v()) : setTimeout(u, 50);
      }, v = () => {
        const R = Date.now();
        let M = {
          currentPointMode: y.pointcloudMode,
          currentLightRadius: 0,
          stop: !1,
          startTime: R
        };
        o.push(M);
        const D = (S) => S;
        s(
          Rt,
          () => {
            if (e) return;
            const S = Date.now() - M.startTime, P = Math.min(S / 1e3, 1), G = D(P);
            M.currentLightRadius = G * i, s(Nt, M.currentLightRadius), P >= 1 && (s(ft, !M.currentPointMode), s(Nt, 0), M.stop = !0, o.length === 1 && o[0] === M && o.pop(), s(Nn, m));
          },
          () => !e && !M.stop
        );
      };
      u();
    } else
      s(ts, performance.now()), s(Nt, 0.1), setTimeout(() => {
        s(ft), s(Nt, 0), s(Nn, m);
      }, 100);
  }), t(Nn, (m = !1) => {
    s(ns, 0), m && s(F).viewerEvents?.fire($t), s(F).viewerEvents?.fire(Oa);
  }), t(Oi, () => ({
    [_s]: { type: "t", value: null },
    [Us]: { type: "t", value: null },
    [Ls]: { type: "t", value: null },
    [Ps]: { type: "t", value: null },
    [Fo]: { type: "v2", value: new ye() },
    [Ro]: { type: "v2", value: new ye() },
    [xo]: { type: "int", value: 0 },
    [mo]: { type: "int", value: 1 },
    [Do]: { type: "bool", value: !1 },
    [Mo]: { type: "bool", value: !0 },
    [So]: { type: "bool", value: !1 },
    [To]: { type: "int", value: 0 },
    [vo]: { type: "float", value: 1 },
    [Po]: { type: "float", value: 0 },
    [_o]: { type: "v3", value: new k() },
    [Uo]: { type: "v3", value: new k() },
    [fo]: { type: "float", value: 0 },
    [po]: { type: "float", value: 0 },
    [Qo]: { type: "float", value: 0 },
    [ko]: { type: "v4", value: new Xe(0, 0, 0, -1) },
    [bo]: { type: "int", value: 0 },
    [Io]: { type: "float", value: performance.now() },
    [Mn]: { type: "float", value: 0 },
    [yo]: { type: "float", value: 1 },
    [Co]: { type: "float", value: 1024 },
    [Lo]: { type: "float", value: 2 / 255 },
    [Ll]: { type: "v4", value: new Xe(1, 1, 0, 0.5) },
    [wo]: { type: "bool", value: !0 },
    [Eo]: { type: "bool", value: !0 },
    [Bo]: { type: "uint", value: 1 },
    [Fs]: { type: "uint", value: 0 }
  }));
  const h = s(pa);
  h.onmessage = (m) => {
    const y = m.data;
    if (y[No]) {
      r = y[iA], l = y[hi];
      const g = y[No];
      s(Zi, g, y[Wo], y[Hl], y[Wl], y[Ho]), g.length && h.postMessage({ [Pl]: g }, [g.buffer]);
    }
  }, t(si, (m, y, g) => {
    s(Ee) || (a = y, i = g), s(ji, m);
  });
  function p(m) {
    const y = m.xyz.slice(0);
    h.postMessage(
      {
        [Yl]: !0,
        [Ol]: y,
        [eA]: m.watermarkCount,
        [Wo]: m.index,
        [_l]: m.version,
        [Ho]: m.renderSplatCount,
        [Ul]: m.visibleSplatCount,
        [Nl]: m.modelSplatCount,
        [Xl]: m.minX,
        [Jl]: m.maxX,
        [Kl]: m.minY,
        [Zl]: m.maxY,
        [jl]: m.minZ,
        [ql]: m.maxZ
      },
      [y.buffer]
    );
  }
}
function dA(n) {
  const e = (i, ...a) => n.fire(i, ...a), t = (i, a, o) => n.on(i, a, o), s = new Set(Qc.split(""));
  t(Va, async (i = "", a = !0, o = !0) => {
    const r = i.trim().substring(0, 100);
    let l = await e(ga, r), A = [];
    for (let v = 0; v < l.length; v++) {
      let x = [], R = l[v];
      for (let M = 0; M < R.length; M++)
        x.push([(R[M] % 20 - 10) * 0.02, ((R[M] / 20 | 0) - 10) * 0.02]);
      A.push(x);
    }
    let c = [], d = r.split("");
    for (let v = 0; v < d.length; v++)
      c[v] = s.has(d[v]) ? 0.22 : 0.4;
    let h = d.length / 2 | 0, p = c[h] / 2, m = !(d.length % 2), y = m ? 0 : -p;
    for (let v = h - 1; v >= 0; v--) {
      y -= c[v] / 2;
      for (let x of A[v]) x[0] += y;
      y -= c[v] / 2;
    }
    p = c[h] / 2, y = m ? 0 : p;
    for (let v = A.length - h; v < A.length; v++) {
      y += c[v] / 2;
      for (let x of A[v]) x[0] += y;
      y += c[v] / 2;
    }
    let g = 0;
    for (let v of A)
      g += v.length;
    const f = new Uint8Array(g * X);
    let u = 0;
    for (let v of A)
      for (let x of v)
        f.set(await bl(x[0], x[1], a, o), X * u++);
    return f;
  });
}
function _i(n) {
  const e = (t, s, i) => n.on(t, s, i);
  e(je, (t) => {
    t.url;
    const s = { ...t };
    delete s.url;
    const i = JSON.stringify(s, null, 2);
    console.info(i);
  }), e(ga, (t = "") => {
    const s = "https://reall3d.com/gsfont/api/getGaussianText", i = new FormData();
    return i.append("text", t.substring(0, 100)), i.append("ver", ds), new Promise((a) => {
      fetch(s, { method: "POST", body: i }).then((o) => o.ok ? o.json() : {}).then((o) => o.success ? a(JSON.parse(o.data)) : a([])).catch((o) => a([]));
    });
  });
}
const uA = /^[og]\s*(.+)?/, hA = /^mtllib /, gA = /^usemtl /, fA = /^usemap /, Vo = /\s+/, Oo = new k(), Ns = new k(), Xo = new k(), Jo = new k(), Te = new k(), xn = new yi();
function pA() {
  const n = {
    objects: [],
    object: {},
    vertices: [],
    normals: [],
    colors: [],
    uvs: [],
    materials: {},
    materialLibraries: [],
    startObject: function(e, t) {
      if (this.object && this.object.fromDeclaration === !1) {
        this.object.name = e, this.object.fromDeclaration = t !== !1;
        return;
      }
      const s = this.object && typeof this.object.currentMaterial == "function" ? this.object.currentMaterial() : void 0;
      if (this.object && typeof this.object._finalize == "function" && this.object._finalize(!0), this.object = {
        name: e || "",
        fromDeclaration: t !== !1,
        geometry: {
          vertices: [],
          normals: [],
          colors: [],
          uvs: [],
          hasUVIndices: !1
        },
        materials: [],
        smooth: !0,
        startMaterial: function(i, a) {
          const o = this._finalize(!1);
          o && (o.inherited || o.groupCount <= 0) && this.materials.splice(o.index, 1);
          const r = {
            index: this.materials.length,
            name: i || "",
            mtllib: Array.isArray(a) && a.length > 0 ? a[a.length - 1] : "",
            smooth: o !== void 0 ? o.smooth : this.smooth,
            groupStart: o !== void 0 ? o.groupEnd : 0,
            groupEnd: -1,
            groupCount: -1,
            inherited: !1,
            clone: function(l) {
              const A = {
                index: typeof l == "number" ? l : this.index,
                name: this.name,
                mtllib: this.mtllib,
                smooth: this.smooth,
                groupStart: 0,
                groupEnd: -1,
                groupCount: -1,
                inherited: !1
              };
              return A.clone = this.clone.bind(A), A;
            }
          };
          return this.materials.push(r), r;
        },
        currentMaterial: function() {
          if (this.materials.length > 0)
            return this.materials[this.materials.length - 1];
        },
        _finalize: function(i) {
          const a = this.currentMaterial();
          if (a && a.groupEnd === -1 && (a.groupEnd = this.geometry.vertices.length / 3, a.groupCount = a.groupEnd - a.groupStart, a.inherited = !1), i && this.materials.length > 1)
            for (let o = this.materials.length - 1; o >= 0; o--)
              this.materials[o].groupCount <= 0 && this.materials.splice(o, 1);
          return i && this.materials.length === 0 && this.materials.push({
            name: "",
            smooth: this.smooth
          }), a;
        }
      }, s && s.name && typeof s.clone == "function") {
        const i = s.clone(0);
        i.inherited = !0, this.object.materials.push(i);
      }
      this.objects.push(this.object);
    },
    finalize: function() {
      this.object && typeof this.object._finalize == "function" && this.object._finalize(!0);
    },
    parseVertexIndex: function(e, t) {
      const s = parseInt(e, 10);
      return (s >= 0 ? s - 1 : s + t / 3) * 3;
    },
    parseNormalIndex: function(e, t) {
      const s = parseInt(e, 10);
      return (s >= 0 ? s - 1 : s + t / 3) * 3;
    },
    parseUVIndex: function(e, t) {
      const s = parseInt(e, 10);
      return (s >= 0 ? s - 1 : s + t / 2) * 2;
    },
    addVertex: function(e, t, s) {
      const i = this.vertices, a = this.object.geometry.vertices;
      a.push(i[e + 0], i[e + 1], i[e + 2]), a.push(i[t + 0], i[t + 1], i[t + 2]), a.push(i[s + 0], i[s + 1], i[s + 2]);
    },
    addVertexPoint: function(e) {
      const t = this.vertices;
      this.object.geometry.vertices.push(t[e + 0], t[e + 1], t[e + 2]);
    },
    addVertexLine: function(e) {
      const t = this.vertices;
      this.object.geometry.vertices.push(t[e + 0], t[e + 1], t[e + 2]);
    },
    addNormal: function(e, t, s) {
      const i = this.normals, a = this.object.geometry.normals;
      a.push(i[e + 0], i[e + 1], i[e + 2]), a.push(i[t + 0], i[t + 1], i[t + 2]), a.push(i[s + 0], i[s + 1], i[s + 2]);
    },
    addFaceNormal: function(e, t, s) {
      const i = this.vertices, a = this.object.geometry.normals;
      Oo.fromArray(i, e), Ns.fromArray(i, t), Xo.fromArray(i, s), Te.subVectors(Xo, Ns), Jo.subVectors(Oo, Ns), Te.cross(Jo), Te.normalize(), a.push(Te.x, Te.y, Te.z), a.push(Te.x, Te.y, Te.z), a.push(Te.x, Te.y, Te.z);
    },
    addColor: function(e, t, s) {
      const i = this.colors, a = this.object.geometry.colors;
      i[e] !== void 0 && a.push(i[e + 0], i[e + 1], i[e + 2]), i[t] !== void 0 && a.push(i[t + 0], i[t + 1], i[t + 2]), i[s] !== void 0 && a.push(i[s + 0], i[s + 1], i[s + 2]);
    },
    addUV: function(e, t, s) {
      const i = this.uvs, a = this.object.geometry.uvs;
      a.push(i[e + 0], i[e + 1]), a.push(i[t + 0], i[t + 1]), a.push(i[s + 0], i[s + 1]);
    },
    addDefaultUV: function() {
      const e = this.object.geometry.uvs;
      e.push(0, 0), e.push(0, 0), e.push(0, 0);
    },
    addUVLine: function(e) {
      const t = this.uvs;
      this.object.geometry.uvs.push(t[e + 0], t[e + 1]);
    },
    addFace: function(e, t, s, i, a, o, r, l, A) {
      const c = this.vertices.length;
      let d = this.parseVertexIndex(e, c), h = this.parseVertexIndex(t, c), p = this.parseVertexIndex(s, c);
      if (this.addVertex(d, h, p), this.addColor(d, h, p), r !== void 0 && r !== "") {
        const m = this.normals.length;
        d = this.parseNormalIndex(r, m), h = this.parseNormalIndex(l, m), p = this.parseNormalIndex(A, m), this.addNormal(d, h, p);
      } else
        this.addFaceNormal(d, h, p);
      if (i !== void 0 && i !== "") {
        const m = this.uvs.length;
        d = this.parseUVIndex(i, m), h = this.parseUVIndex(a, m), p = this.parseUVIndex(o, m), this.addUV(d, h, p), this.object.geometry.hasUVIndices = !0;
      } else
        this.addDefaultUV();
    },
    addPointGeometry: function(e) {
      this.object.geometry.type = "Points";
      const t = this.vertices.length;
      for (let s = 0, i = e.length; s < i; s++) {
        const a = this.parseVertexIndex(e[s], t);
        this.addVertexPoint(a), this.addColor(a);
      }
    },
    addLineGeometry: function(e, t) {
      this.object.geometry.type = "Line";
      const s = this.vertices.length, i = this.uvs.length;
      for (let a = 0, o = e.length; a < o; a++)
        this.addVertexLine(this.parseVertexIndex(e[a], s));
      for (let a = 0, o = t.length; a < o; a++)
        this.addUVLine(this.parseUVIndex(t[a], i));
    }
  };
  return n.startObject("", !1), n;
}
class mA extends Pr {
  constructor(e) {
    super(e), this.materials = null;
  }
  load(e, t, s, i) {
    const a = this, o = new _r(this.manager);
    o.setPath(this.path), o.setRequestHeader(this.requestHeader), o.setWithCredentials(this.withCredentials), o.load(e, function(r) {
      try {
        t(a.parse(r));
      } catch (l) {
        i ? i(l) : console.error(l), a.manager.itemError(e);
      }
    }, s, i);
  }
  setMaterials(e) {
    return this.materials = e, this;
  }
  parse(e) {
    const t = new pA();
    e.indexOf(`\r
`) !== -1 && (e = e.replace(/\r\n/g, `
`)), e.indexOf(`\\
`) !== -1 && (e = e.replace(/\\\n/g, ""));
    const s = e.split(`
`);
    let i = [];
    for (let r = 0, l = s.length; r < l; r++) {
      const A = s[r].trimStart();
      if (A.length === 0) continue;
      const c = A.charAt(0);
      if (c !== "#")
        if (c === "v") {
          const d = A.split(Vo);
          switch (d[0]) {
            case "v":
              t.vertices.push(
                parseFloat(d[1]),
                parseFloat(d[2]),
                parseFloat(d[3])
              ), d.length >= 7 ? (xn.setRGB(
                parseFloat(d[4]),
                parseFloat(d[5]),
                parseFloat(d[6]),
                Ur
              ), t.colors.push(xn.r, xn.g, xn.b)) : t.colors.push(void 0, void 0, void 0);
              break;
            case "vn":
              t.normals.push(
                parseFloat(d[1]),
                parseFloat(d[2]),
                parseFloat(d[3])
              );
              break;
            case "vt":
              t.uvs.push(
                parseFloat(d[1]),
                parseFloat(d[2])
              );
              break;
          }
        } else if (c === "f") {
          const h = A.slice(1).trim().split(Vo), p = [];
          for (let y = 0, g = h.length; y < g; y++) {
            const f = h[y];
            if (f.length > 0) {
              const u = f.split("/");
              p.push(u);
            }
          }
          const m = p[0];
          for (let y = 1, g = p.length - 1; y < g; y++) {
            const f = p[y], u = p[y + 1];
            t.addFace(
              m[0],
              f[0],
              u[0],
              m[1],
              f[1],
              u[1],
              m[2],
              f[2],
              u[2]
            );
          }
        } else if (c === "l") {
          const d = A.substring(1).trim().split(" ");
          let h = [];
          const p = [];
          if (A.indexOf("/") === -1)
            h = d;
          else
            for (let m = 0, y = d.length; m < y; m++) {
              const g = d[m].split("/");
              g[0] !== "" && h.push(g[0]), g[1] !== "" && p.push(g[1]);
            }
          t.addLineGeometry(h, p);
        } else if (c === "p") {
          const h = A.slice(1).trim().split(" ");
          t.addPointGeometry(h);
        } else if ((i = uA.exec(A)) !== null) {
          const d = (" " + i[0].slice(1).trim()).slice(1);
          t.startObject(d);
        } else if (gA.test(A))
          t.object.startMaterial(A.substring(7).trim(), t.materialLibraries);
        else if (hA.test(A))
          t.materialLibraries.push(A.substring(7).trim());
        else if (fA.test(A))
          console.warn('THREE.OBJLoader: Rendering identifier "usemap" not supported. Textures must be defined in MTL files.');
        else if (c === "s") {
          if (i = A.split(" "), i.length > 1) {
            const h = i[1].trim().toLowerCase();
            t.object.smooth = h !== "0" && h !== "off";
          } else
            t.object.smooth = !0;
          const d = t.object.currentMaterial();
          d && (d.smooth = t.object.smooth);
        } else {
          if (A === "\0") continue;
          console.warn('THREE.OBJLoader: Unexpected line: "' + A + '"');
        }
    }
    t.finalize();
    const a = new on();
    if (a.materialLibraries = [].concat(t.materialLibraries), !(t.objects.length === 1 && t.objects[0].geometry.vertices.length === 0) === !0)
      for (let r = 0, l = t.objects.length; r < l; r++) {
        const A = t.objects[r], c = A.geometry, d = A.materials, h = c.type === "Line", p = c.type === "Points";
        let m = !1;
        if (c.vertices.length === 0) continue;
        const y = new Yn();
        y.setAttribute("position", new Ze(c.vertices, 3)), c.normals.length > 0 && y.setAttribute("normal", new Ze(c.normals, 3)), c.colors.length > 0 && (m = !0, y.setAttribute("color", new Ze(c.colors, 3))), c.hasUVIndices === !0 && y.setAttribute("uv", new Ze(c.uvs, 2));
        const g = [];
        for (let u = 0, v = d.length; u < v; u++) {
          const x = d[u], R = x.name + "_" + x.smooth + "_" + m;
          let M = t.materials[R];
          if (this.materials !== null) {
            if (M = this.materials.create(x.name), h && M && !(M instanceof Tn)) {
              const D = new Tn();
              Wi.prototype.copy.call(D, M), D.color.copy(M.color), M = D;
            } else if (p && M && !(M instanceof un)) {
              const D = new un({ size: 10, sizeAttenuation: !1 });
              Wi.prototype.copy.call(D, M), D.color.copy(M.color), D.map = M.map, M = D;
            }
          }
          M === void 0 && (h ? M = new Tn() : p ? M = new un({ size: 1, sizeAttenuation: !1 }) : M = new Nr(), M.name = x.name, M.flatShading = !x.smooth, M.vertexColors = m, t.materials[R] = M), g.push(M);
        }
        let f;
        if (g.length > 1) {
          for (let u = 0, v = d.length; u < v; u++) {
            const x = d[u];
            y.addGroup(x.groupStart, x.groupCount, u);
          }
          h ? f = new Vs(y, g) : p ? f = new fs(y, g) : f = new Ie(y, g);
        } else
          h ? f = new Vs(y, g[0]) : p ? f = new fs(y, g[0]) : f = new Ie(y, g[0]);
        f.name = A.name, a.add(f);
      }
    else if (t.vertices.length > 0) {
      const r = new un({ size: 1, sizeAttenuation: !1 }), l = new Yn();
      l.setAttribute("position", new Ze(t.vertices, 3)), t.colors.length > 0 && t.colors[0] !== void 0 && (l.setAttribute("color", new Ze(t.colors, 3)), r.vertexColors = !0);
      const A = new fs(l, r);
      a.add(A);
    }
    return a;
  }
}
function CA(n) {
  let e = !1;
  const t = (r, l, A) => n.on(r, l, A), s = (r, ...l) => n.fire(r, ...l);
  t(Ba, () => e = !0);
  const i = /* @__PURE__ */ new Map(), a = /* @__PURE__ */ new Map();
  t(zn, () => s(Qe) && i.set(Date.now(), 1)), t(Vn, () => s(Qe) && s(Dt, i)), t(os, () => s(Qe) && a.set(Date.now(), 1)), t(On, () => s(Qe) && s(Dt, a));
  let o = 0;
  t(
    kt,
    () => {
      e || (s(Ca), s(Qe) && (s(zn), !(o++ % 5) && s(ce, {
        fps: s(Vn),
        realFps: s(On),
        fov: s(an),
        position: s(mt, s(Ue)),
        lookAt: s(mt, s(Ve)),
        lookUp: s(mt, s(Lt))
      })));
    },
    !0
  ), t(Dt, (r) => {
    let l = [], A = Date.now(), c = 0;
    for (const d of r.keys())
      A - d <= 1e3 ? c++ : l.push(d);
    return l.forEach((d) => r.delete(d)), c;
  }), window.addEventListener("beforeunload", () => s(as)), t($a, async (r) => {
    s(ce, { scene: "small (obj)" });
    const l = await Ke(r, n);
    if (l) {
      const A = URL.createObjectURL(new Blob([l], { type: "application/octet-stream" }));
      new mA().load(A, (c) => s(V).add(c)), s(j, !0), s(ot, !0);
    }
    s(Jt, 0);
  });
}
function yA(n) {
  const e = { ...n };
  return e.bigSceneMode ?? (e.bigSceneMode = !1), e.pointcloudMode ?? (e.pointcloudMode = !e.bigSceneMode), e.lightFactor ?? (e.lightFactor = 1), e.name ?? (e.name = ""), e.showWatermark ?? (e.showWatermark = !1), e.shDegree ?? (e.shDegree = 0), e.depthTest ?? (e.depthTest = !0), e.debugMode ?? (e.debugMode = !1), e.transitionAnimDuration ?? (e.transitionAnimDuration = 4e3), e.maxRenderCountOfMobile ?? (e.maxRenderCountOfMobile = e.bigSceneMode ? 256 * 1e4 : 384 * 10240), e.maxRenderCountOfPc ?? (e.maxRenderCountOfPc = e.bigSceneMode ? 320 * 1e4 : 384 * 1e4), e;
}
function Ko(n) {
  const e = { ...n };
  return e.position = e.position ? [...e.position] : [0, -5, 15], e.lookAt = e.lookAt ? [...e.lookAt] : [0, 0, 0], e.lookUp = e.lookUp ? [...e.lookUp] : [0, -1, 0], e.fov ?? (e.fov = 45), e.near ?? (e.near = 0.1), e.far ?? (e.far = 1e3), e.enableDamping ?? (e.enableDamping = !0), e.autoRotate ?? (e.autoRotate = !0), e.enableZoom ?? (e.enableZoom = !0), e.enableRotate ?? (e.enableRotate = !0), e.enablePan ?? (e.enablePan = !0), e.enableKeyboard ?? (e.enableKeyboard = !0), e.bigSceneMode ?? (e.bigSceneMode = !1), e.pointcloudMode ?? (e.pointcloudMode = !e.bigSceneMode), e.lightFactor ?? (e.lightFactor = 1.1), e.debugMode ?? (e.debugMode = location.protocol === "http:" || /^test\./.test(location.host)), e.markMode ?? (e.markMode = !1), e.markVisible ?? (e.markVisible = !0), e.meterScale ?? (e.meterScale = 1), e.background ?? (e.background = "#000000"), e.minDistance ?? (e.minDistance = 0.1), e.maxDistance ?? (e.maxDistance = 1e3), e.minPolarAngle ?? (e.minPolarAngle = 0), e.maxPolarAngle ?? (e.maxPolarAngle = Math.PI / 2), e.qualityLevel ?? (e.qualityLevel = Be.Default5), e.sortType ?? (e.sortType = tn.Default1), e.transitionEffect ?? (e.transitionEffect = hs.ModelCenterCirccle), e;
}
function IA(n) {
  let e;
  n.root ? e = typeof n.root == "string" ? document.querySelector(n.root) || document.querySelector("#gsviewer") : n.root : e = document.querySelector("#gsviewer"), e || (e = document.createElement("div"), e.id = "gsviewer", document.body.appendChild(e));
  let t = null;
  return n.renderer ? t = n.renderer : (t = new ra({
    antialias: !1,
    stencil: !1,
    logarithmicDepthBuffer: !0,
    premultipliedAlpha: !1,
    precision: "highp",
    powerPreference: "high-performance"
  }), t.setSize(e.clientWidth, e.clientHeight), t.setPixelRatio(Math.min(devicePixelRatio, 2)), n.renderer = t), t.domElement.classList.add("gsviewer-canvas"), e.appendChild(t.domElement), t;
}
function wA(n) {
  let e = n.camera;
  if (!e) {
    const t = n.renderer.domElement, s = t.width / t.height;
    let i = new k().fromArray(n.lookUp), a = new k().fromArray(n.lookAt), o = new k().fromArray(n.position);
    e = new ca(n.fov, s, n.near, n.far), e.position.copy(o), e.up.copy(i).normalize(), e.lookAt(a), n.camera = e;
  }
  return n.camera;
}
function EA(n) {
  const { renderer: e, scene: t } = n, s = { renderer: e, scene: t };
  return s.viewerEvents = n.viewerEvents, s.debugMode = n.debugMode, s.renderer = n.renderer, s.scene = n.scene, s.controls = n.controls, s.bigSceneMode = n.bigSceneMode, s.pointcloudMode = n.pointcloudMode, s.maxRenderCountOfMobile = n.maxRenderCountOfMobile, s.maxRenderCountOfPc = n.maxRenderCountOfPc, s.lightFactor = n.lightFactor, s.shDegree = n.shDegree, s.qualityLevel = n.qualityLevel, s.sortType = n.sortType, s.disableTransitionEffectOnLoad = !!n.disableTransitionEffectOnLoad, s.transitionEffect = n.transitionEffect || hs.ModelCenterCirccle, s.transitionAnimDuration = n.transitionAnimDuration, s.disableProgressiveLoading = n.disableProgressiveLoading, s.disableStreamLoading = n.disableStreamLoading, s.onPerformanceUpdate = n.onPerformanceUpdate, s;
}
function bA(n) {
  const e = (o, r, l) => n.on(o, r, l), t = (o, ...r) => n.fire(o, ...r), s = "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", i = URL.createObjectURL(new Blob([atob(s)], { type: "text/javascript" })), a = new Worker(new URL(i, import.meta.url), { type: "module" });
  e(pa, () => a), e(
    Ea,
    () => a.postMessage({
      [Vl]: t(Ia),
      [tA]: t(Wa),
      [nA]: t(Ue).toArray()
    })
  ), e(ba, () => a.terminate()), e(
    Ti,
    () => a.postMessage({
      [sA]: !0,
      [Yo]: t(li),
      [hi]: t(Ai),
      [Go]: t(ve)?.meta?.depthNearRate,
      [zo]: t(ve)?.meta?.depthNearValue
    })
  ), (async () => a.postMessage({
    [$l]: !0,
    [zl]: await t(pt),
    [Gl]: t(Ee),
    [Yo]: t(li),
    [hi]: t(Ai),
    [Go]: t(ve)?.meta?.depthNearRate,
    [zo]: t(ve)?.meta?.depthNearValue
  }))();
}
class SA extends is {
  constructor(e = 0, t = 0, s = 0, i = 0, a = 0, o = 0) {
    super();
    const r = this, l = new Yn(), A = new Tn({ color: "#ffffff" });
    r.boxLines = new Vs(l, A), r.update(e, t, s, i, a, o), r.add(r.boxLines);
  }
  update(e, t, s, i, a, o, r) {
    const l = (i - e) / 8, A = (a - t) / 8, c = (o - s) / 8, d = [
      new k(e, t, s),
      // 左下前
      new k(i, t, s),
      // 右下前
      new k(i, a, s),
      // 右上前
      new k(e, a, s),
      // 左上前
      new k(e, t, o),
      // 左下后
      new k(i, t, o),
      // 右下后
      new k(i, a, o),
      // 右上后
      new k(e, a, o)
      // 左上后
    ], h = [];
    d.forEach((p) => {
      h.push(p.x, p.y, p.z), h.push(p.x + (p.x < i ? l : -l), p.y, p.z), h.push(p.x, p.y, p.z), h.push(p.x, p.y + (p.y < a ? A : -A), p.z), h.push(p.x, p.y, p.z), h.push(p.x, p.y, p.z + (p.z < o ? c : -c));
    }), this.boxLines.geometry.setAttribute("position", new Ze(h, 3)), r && (this.visible = !0);
  }
  dispose() {
    this.boxLines = null;
  }
}
class Le extends Ie {
  /**
   * 构造函数
   * @param options 渲染器、场景、相机都应该传入
   */
  constructor(e) {
    super(), this.isSplatMesh = !0, this.disposed = !1;
    const t = this, s = new As(), i = (c, d, h) => s.on(c, d, h), a = (c, ...d) => s.fire(c, ...d);
    let o = !1;
    const r = yA(e), l = r.camera || r.controls?.object;
    i(F, () => r), i(_t, () => r.renderer.domElement), i($, () => l), i(an, () => l.fov), i(Ue, (c = !1) => c ? l.position.clone() : l.position), i(Ve, (c = !1) => r.controls ? c ? r.controls.target.clone() : r.controls.target : new k()), i(Ia, () => l.projectionMatrix.clone().multiply(l.matrixWorldInverse).multiply(t.matrix).toArray()), i(wa, () => l.projectionMatrix.clone().multiply(l.matrixWorldInverse)), i(Wa, () => l.getWorldDirection(new k()).toArray()), i(qt, () => r.renderer), i(V, () => r.scene), i(Ee, () => r.bigSceneMode), i(Da, () => r.pointcloudMode), i(ve, () => t), i(li, () => r.qualityLevel || Be.Default5), i(Ai, () => r.sortType || tn.Default1), i(nr, () => o), i(ne, () => r.viewerEvents?.fire(j)), Li(s), _i(s), Rl(s), bA(s), AA(s), dA(s), t.name = `${r.name || t.id}`, t.events = s, t.opts = r, (async () => (t.copy(await s.fire(Sa)), t.meta.transform && t.applyMatrix4(new $e().fromArray(t.meta.transform)), t.frustumCulled = !1, t.onBeforeRender = () => {
      a(Ea), a(Qa, performance.now());
    }, t.onAfterRender = () => {
      a(Ta, 1e4) && a(ne);
    }, o = !0, a(oi)))();
    const A = new SA();
    A.visible = !1, A.renderOrder = 99999, t.boundBox = A, t.add(A), i(ii, (c, d, h, p, m, y, g) => {
      A.update(c, d, h, p, m, y, g);
    }), i(hc, (c = !0) => A.visible = c);
  }
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(e) {
    const t = this;
    if (t.disposed) return;
    const s = (a, ...o) => t.events.fire(a, ...o), i = t.opts;
    return e && (e.pointcloudMode !== void 0 && s(ft, e.pointcloudMode), e.lightFactor !== void 0 && s(jt, e.lightFactor), e.maxRenderCountOfMobile !== void 0 && (i.maxRenderCountOfMobile = e.maxRenderCountOfMobile), e.maxRenderCountOfPc !== void 0 && (i.maxRenderCountOfPc = e.maxRenderCountOfPc), e.qualityLevel !== void 0 && (i.qualityLevel = e.qualityLevel) && s(oi), !i.mapMode && e.sortType !== void 0 && (i.sortType = e.sortType) && s(Ti), s(ne)), { ...i };
  }
  /**
   * 添加渲染指定高斯模型
   * @param opts 高斯模型选项
   * @param meta 元数据
   */
  addModel(e, t = {}) {
    const s = this;
    s.disposed || (s.meta = t, s.events.fire(ka, e, t));
  }
  fire(e, ...t) {
    const s = this;
    if (!s.disposed)
      return s.events.fire(e, ...t);
  }
  /**
   * 销毁
   */
  dispose() {
    const e = this;
    if (e.disposed) return;
    e.disposed = !0;
    const t = (s, ...i) => e.events.fire(s, ...i);
    t(tt, e), t(tt, e.boundBox), t(V).remove(e), t(V).remove(e.boundBox), t(Ei), t(Ra), t(ba), t(La), e.events.clear(), e.events = null, e.opts = null, e.onAfterRender = null, e.boundBox = null;
  }
}
class vA extends is {
  constructor(e = new k(0, 0, 1), t = new k(0, 0, 0), s = 1, i = 0.1, a = 16776960, o = s * 0.2, r = o * 0.2) {
    super(), this._axis = new k(), this.type = "ArrowHelper";
    const l = new Hi(i, i, s, 32);
    l.translate(0, s / 2, 0);
    const A = new Hi(0, r, o, 32);
    A.translate(0, s, 0), this.position.copy(t);
    const c = new at({ color: a, toneMapped: !1 });
    c.side = et, this.line = new Ie(l, c), this.line.matrixAutoUpdate = !1, this.line.ignoreIntersect = !0, this.add(this.line);
    const d = new at({ color: a, toneMapped: !1 });
    d.side = et, this.cone = new Ie(A, d), this.cone.matrixAutoUpdate = !1, this.cone.ignoreIntersect = !0, this.add(this.cone), this.setDirection(e), this.renderOrder = 99999;
  }
  setDirection(e) {
    if (e.y > 0.99999)
      this.quaternion.set(0, 0, 0, 1);
    else if (e.y < -0.99999)
      this.quaternion.set(1, 0, 0, 0);
    else {
      this._axis.set(e.z, 0, -e.x).normalize();
      const t = Math.acos(e.y);
      this.quaternion.setFromAxisAngle(this._axis, t);
    }
  }
  setColor(e) {
    this.line.material.color.set(e), this.cone.material.color.set(e);
  }
  copy(e) {
    return super.copy(e, !1), this.line.copy(e.line), this.cone.copy(e.cone), this;
  }
  dispose() {
    this.line.geometry.dispose(), this.line.material.dispose(), this.cone.geometry.dispose(), this.cone.material.dispose();
  }
}
function MA(n) {
  const e = (y, g, f) => n.on(y, g, f), t = (y, ...g) => n.fire(y, ...g), s = new Wr(1, 1);
  s.rotateX(-Math.PI / 2);
  const i = new at({ color: 16777215 });
  i.transparent = !0, i.opacity = 0.6, i.depthTest = !1, i.depthWrite = !1, i.side = et;
  const a = new Ie(s, i);
  a.ignoreIntersect = !0;
  const o = new k(0, -1, 0);
  o.normalize();
  const r = new k(0, 0, 0), l = 0.5, A = 0.01, c = 16777062, d = 0.1, h = 0.03, p = new vA(o, r, l, A, c, d, h), m = new is();
  m.add(a), m.add(p), m.renderOrder = 99999, a.renderOrder = 99999, m.visible = !1, t(V).add(m), e(ic, () => m), e(_n, (y) => {
    t(Zn, !0), m.visible = y === void 0 ? !m.visible : y, t(j);
  }), e(Mi, () => m.visible), e(Zn, (y = !1) => {
    if (y || m.visible) {
      const g = new rt(), f = new k(0, -1, 0);
      g.setFromUnitVectors(f, t(Lt)), m.position.copy(t(Ve)), m.quaternion.copy(g);
    }
  });
}
const Zo = { type: "change" }, Ui = { type: "start" }, yr = { type: "end" }, Qn = new Yr(), jo = new Gr(), xA = Math.cos(70 * it.DEG2RAD), Ae = new k(), be = 2 * Math.PI, oe = {
  NONE: -1,
  ROTATE: 0,
  DOLLY: 1,
  PAN: 2,
  TOUCH_ROTATE: 3,
  TOUCH_PAN: 4,
  TOUCH_DOLLY_PAN: 5,
  TOUCH_DOLLY_ROTATE: 6
}, Ws = 1e-6;
class Ir extends Hr {
  constructor(e, t = null) {
    super(e, t), this.state = oe.NONE, this.enabled = !0, this.target = new k(), this.cursor = new k(), this.minDistance = 0, this.maxDistance = 1 / 0, this.minZoom = 0, this.maxZoom = 1 / 0, this.minTargetRadius = 0, this.maxTargetRadius = 1 / 0, this.minPolarAngle = 0, this.maxPolarAngle = Math.PI, this.minAzimuthAngle = -1 / 0, this.maxAzimuthAngle = 1 / 0, this.enableDamping = !1, this.dampingFactor = 0.05, this.enableZoom = !0, this.zoomSpeed = 1, this.enableRotate = !0, this.rotateSpeed = 1, this.enablePan = !0, this.panSpeed = 1, this.screenSpacePanning = !0, this.keyPanSpeed = 7, this.zoomToCursor = !1, this.autoRotate = !1, this.autoRotateSpeed = 2, this.keys = { LEFT: "ArrowLeft", UP: "ArrowUp", RIGHT: "ArrowRight", BOTTOM: "ArrowDown" }, this.mouseButtons = { LEFT: qe.ROTATE, MIDDLE: qe.DOLLY, RIGHT: qe.PAN }, this.touches = { ONE: st.ROTATE, TWO: st.DOLLY_PAN }, this.target0 = this.target.clone(), this.position0 = this.object.position.clone(), this.zoom0 = this.object.zoom, this._domElementKeyEvents = null, this._lastPosition = new k(), this._lastQuaternion = new rt(), this._lastTargetPosition = new k(), this._quat = new rt().setFromUnitVectors(e.up, new k(0, 1, 0)), this._quatInverse = this._quat.clone().invert(), this._spherical = new Yi(), this._sphericalDelta = new Yi(), this._scale = 1, this._panOffset = new k(), this._rotateStart = new ye(), this._rotateEnd = new ye(), this._rotateDelta = new ye(), this._panStart = new ye(), this._panEnd = new ye(), this._panDelta = new ye(), this._dollyStart = new ye(), this._dollyEnd = new ye(), this._dollyDelta = new ye(), this._dollyDirection = new k(), this._mouse = new ye(), this._performCursorZoom = !1, this._pointers = [], this._pointerPositions = {}, this._controlActive = !1, this._onPointerMove = BA.bind(this), this._onPointerDown = QA.bind(this), this._onPointerUp = DA.bind(this), this._onContextMenu = _A.bind(this), this._onMouseWheel = RA.bind(this), this._onKeyDown = LA.bind(this), this._onTouchStart = FA.bind(this), this._onTouchMove = PA.bind(this), this._onMouseDown = kA.bind(this), this._onMouseMove = TA.bind(this), this._interceptControlDown = UA.bind(this), this._interceptControlUp = NA.bind(this), this.domElement !== null && this.connect(), this.update();
  }
  connect() {
    this.domElement.addEventListener("pointerdown", this._onPointerDown), this.domElement.addEventListener("pointercancel", this._onPointerUp), this.domElement.addEventListener("contextmenu", this._onContextMenu), this.domElement.addEventListener("wheel", this._onMouseWheel, { passive: !1 }), this.domElement.getRootNode().addEventListener("keydown", this._interceptControlDown, { passive: !0, capture: !0 }), this.domElement.style.touchAction = "none";
  }
  disconnect() {
    this.domElement.removeEventListener("pointerdown", this._onPointerDown), this.domElement.removeEventListener("pointermove", this._onPointerMove), this.domElement.removeEventListener("pointerup", this._onPointerUp), this.domElement.removeEventListener("pointercancel", this._onPointerUp), this.domElement.removeEventListener("wheel", this._onMouseWheel), this.domElement.removeEventListener("contextmenu", this._onContextMenu), this.stopListenToKeyEvents(), this.domElement.getRootNode().removeEventListener("keydown", this._interceptControlDown, { capture: !0 }), this.domElement.style.touchAction = "auto";
  }
  dispose() {
    this.disconnect();
  }
  getPolarAngle() {
    return this._spherical.phi;
  }
  getAzimuthalAngle() {
    return this._spherical.theta;
  }
  getDistance() {
    return this.object.position.distanceTo(this.target);
  }
  listenToKeyEvents(e) {
    e.addEventListener("keydown", this._onKeyDown), this._domElementKeyEvents = e;
  }
  stopListenToKeyEvents() {
    this._domElementKeyEvents !== null && (this._domElementKeyEvents.removeEventListener("keydown", this._onKeyDown), this._domElementKeyEvents = null);
  }
  saveState() {
    this.target0.copy(this.target), this.position0.copy(this.object.position), this.zoom0 = this.object.zoom;
  }
  reset() {
    this.target.copy(this.target0), this.object.position.copy(this.position0), this.object.zoom = this.zoom0, this.object.updateProjectionMatrix(), this.dispatchEvent(Zo), this.update(), this.state = oe.NONE;
  }
  update(e = null) {
    const t = this.object.position;
    Ae.copy(t).sub(this.target), Ae.applyQuaternion(this._quat), this._spherical.setFromVector3(Ae), this.autoRotate && this.state === oe.NONE && this._rotateLeft(this._getAutoRotationAngle(e)), this.enableDamping ? (this._spherical.theta += this._sphericalDelta.theta * this.dampingFactor, this._spherical.phi += this._sphericalDelta.phi * this.dampingFactor) : (this._spherical.theta += this._sphericalDelta.theta, this._spherical.phi += this._sphericalDelta.phi);
    let s = this.minAzimuthAngle, i = this.maxAzimuthAngle;
    isFinite(s) && isFinite(i) && (s < -Math.PI ? s += be : s > Math.PI && (s -= be), i < -Math.PI ? i += be : i > Math.PI && (i -= be), s <= i ? this._spherical.theta = Math.max(s, Math.min(i, this._spherical.theta)) : this._spherical.theta = this._spherical.theta > (s + i) / 2 ? Math.max(s, this._spherical.theta) : Math.min(i, this._spherical.theta)), this._spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, this._spherical.phi)), this._spherical.makeSafe(), this.enableDamping === !0 ? this.target.addScaledVector(this._panOffset, this.dampingFactor) : this.target.add(this._panOffset), this.target.sub(this.cursor), this.target.clampLength(this.minTargetRadius, this.maxTargetRadius), this.target.add(this.cursor);
    let a = !1;
    if (this.zoomToCursor && this._performCursorZoom || this.object.isOrthographicCamera)
      this._spherical.radius = this._clampDistance(this._spherical.radius);
    else {
      const o = this._spherical.radius;
      this._spherical.radius = this._clampDistance(this._spherical.radius * this._scale), a = o != this._spherical.radius;
    }
    if (Ae.setFromSpherical(this._spherical), Ae.applyQuaternion(this._quatInverse), t.copy(this.target).add(Ae), this.object.lookAt(this.target), this.enableDamping === !0 ? (this._sphericalDelta.theta *= 1 - this.dampingFactor, this._sphericalDelta.phi *= 1 - this.dampingFactor, this._panOffset.multiplyScalar(1 - this.dampingFactor)) : (this._sphericalDelta.set(0, 0, 0), this._panOffset.set(0, 0, 0)), this.zoomToCursor && this._performCursorZoom) {
      let o = null;
      if (this.object.isPerspectiveCamera) {
        const r = Ae.length();
        o = this._clampDistance(r * this._scale);
        const l = r - o;
        this.object.position.addScaledVector(this._dollyDirection, l), this.object.updateMatrixWorld(), a = !!l;
      } else if (this.object.isOrthographicCamera) {
        const r = new k(this._mouse.x, this._mouse.y, 0);
        r.unproject(this.object);
        const l = this.object.zoom;
        this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / this._scale)), this.object.updateProjectionMatrix(), a = l !== this.object.zoom;
        const A = new k(this._mouse.x, this._mouse.y, 0);
        A.unproject(this.object), this.object.position.sub(A).add(r), this.object.updateMatrixWorld(), o = Ae.length();
      } else
        console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."), this.zoomToCursor = !1;
      o !== null && (this.screenSpacePanning ? this.target.set(0, 0, -1).transformDirection(this.object.matrix).multiplyScalar(o).add(this.object.position) : (Qn.origin.copy(this.object.position), Qn.direction.set(0, 0, -1).transformDirection(this.object.matrix), Math.abs(this.object.up.dot(Qn.direction)) < xA ? this.object.lookAt(this.target) : (jo.setFromNormalAndCoplanarPoint(this.object.up, this.target), Qn.intersectPlane(jo, this.target))));
    } else if (this.object.isOrthographicCamera) {
      const o = this.object.zoom;
      this.object.zoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.object.zoom / this._scale)), o !== this.object.zoom && (this.object.updateProjectionMatrix(), a = !0);
    }
    return this._scale = 1, this._performCursorZoom = !1, a || this._lastPosition.distanceToSquared(this.object.position) > Ws || 8 * (1 - this._lastQuaternion.dot(this.object.quaternion)) > Ws || this._lastTargetPosition.distanceToSquared(this.target) > Ws ? (this.dispatchEvent(Zo), this._lastPosition.copy(this.object.position), this._lastQuaternion.copy(this.object.quaternion), this._lastTargetPosition.copy(this.target), !0) : !1;
  }
  _getAutoRotationAngle(e) {
    return e !== null ? be / 60 * this.autoRotateSpeed * e : be / 60 / 60 * this.autoRotateSpeed;
  }
  _getZoomScale(e) {
    const t = Math.abs(e * 0.01);
    return Math.pow(0.95, this.zoomSpeed * t);
  }
  _rotateLeft(e) {
    this._sphericalDelta.theta -= e;
  }
  _rotateUp(e) {
    this._sphericalDelta.phi -= e;
  }
  _panLeft(e, t) {
    Ae.setFromMatrixColumn(t, 0), Ae.multiplyScalar(-e), this._panOffset.add(Ae);
  }
  _panUp(e, t) {
    this.screenSpacePanning === !0 ? Ae.setFromMatrixColumn(t, 1) : (Ae.setFromMatrixColumn(t, 0), Ae.crossVectors(this.object.up, Ae)), Ae.multiplyScalar(e), this._panOffset.add(Ae);
  }
  // deltaX and deltaY are in pixels; right and down are positive
  _pan(e, t) {
    const s = this.domElement;
    if (this.object.isPerspectiveCamera) {
      const i = this.object.position;
      Ae.copy(i).sub(this.target);
      let a = Ae.length();
      a *= Math.tan(this.object.fov / 2 * Math.PI / 180), this._panLeft(2 * e * a / s.clientHeight, this.object.matrix), this._panUp(2 * t * a / s.clientHeight, this.object.matrix);
    } else this.object.isOrthographicCamera ? (this._panLeft(e * (this.object.right - this.object.left) / this.object.zoom / s.clientWidth, this.object.matrix), this._panUp(t * (this.object.top - this.object.bottom) / this.object.zoom / s.clientHeight, this.object.matrix)) : (console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."), this.enablePan = !1);
  }
  _dollyOut(e) {
    this.object.isPerspectiveCamera || this.object.isOrthographicCamera ? this._scale /= e : (console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."), this.enableZoom = !1);
  }
  _dollyIn(e) {
    this.object.isPerspectiveCamera || this.object.isOrthographicCamera ? this._scale *= e : (console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."), this.enableZoom = !1);
  }
  _updateZoomParameters(e, t) {
    if (!this.zoomToCursor)
      return;
    this._performCursorZoom = !0;
    const s = this.domElement.getBoundingClientRect(), i = e - s.left, a = t - s.top, o = s.width, r = s.height;
    this._mouse.x = i / o * 2 - 1, this._mouse.y = -(a / r) * 2 + 1, this._dollyDirection.set(this._mouse.x, this._mouse.y, 1).unproject(this.object).sub(this.object.position).normalize();
  }
  _clampDistance(e) {
    return Math.max(this.minDistance, Math.min(this.maxDistance, e));
  }
  //
  // event callbacks - update the object state
  //
  _handleMouseDownRotate(e) {
    this._rotateStart.set(e.clientX, e.clientY);
  }
  _handleMouseDownDolly(e) {
    this._updateZoomParameters(e.clientX, e.clientX), this._dollyStart.set(e.clientX, e.clientY);
  }
  _handleMouseDownPan(e) {
    this._panStart.set(e.clientX, e.clientY);
  }
  _handleMouseMoveRotate(e) {
    this._rotateEnd.set(e.clientX, e.clientY), this._rotateDelta.subVectors(this._rotateEnd, this._rotateStart).multiplyScalar(this.rotateSpeed);
    const t = this.domElement;
    this._rotateLeft(be * this._rotateDelta.x / t.clientHeight), this._rotateUp(be * this._rotateDelta.y / t.clientHeight), this._rotateStart.copy(this._rotateEnd), this.update();
  }
  _handleMouseMoveDolly(e) {
    this._dollyEnd.set(e.clientX, e.clientY), this._dollyDelta.subVectors(this._dollyEnd, this._dollyStart), this._dollyDelta.y > 0 ? this._dollyOut(this._getZoomScale(this._dollyDelta.y)) : this._dollyDelta.y < 0 && this._dollyIn(this._getZoomScale(this._dollyDelta.y)), this._dollyStart.copy(this._dollyEnd), this.update();
  }
  _handleMouseMovePan(e) {
    this._panEnd.set(e.clientX, e.clientY), this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed), this._pan(this._panDelta.x, this._panDelta.y), this._panStart.copy(this._panEnd), this.update();
  }
  _handleMouseWheel(e) {
    this._updateZoomParameters(e.clientX, e.clientY), e.deltaY < 0 ? this._dollyIn(this._getZoomScale(e.deltaY)) : e.deltaY > 0 && this._dollyOut(this._getZoomScale(e.deltaY)), this.update();
  }
  _handleKeyDown(e) {
    let t = !1;
    switch (e.code) {
      case this.keys.UP:
        e.ctrlKey || e.metaKey || e.shiftKey ? this.enableRotate && this._rotateUp(be * this.rotateSpeed / this.domElement.clientHeight) : this.enablePan && this._pan(0, this.keyPanSpeed), t = !0;
        break;
      case this.keys.BOTTOM:
        e.ctrlKey || e.metaKey || e.shiftKey ? this.enableRotate && this._rotateUp(-be * this.rotateSpeed / this.domElement.clientHeight) : this.enablePan && this._pan(0, -this.keyPanSpeed), t = !0;
        break;
      case this.keys.LEFT:
        e.ctrlKey || e.metaKey || e.shiftKey ? this.enableRotate && this._rotateLeft(be * this.rotateSpeed / this.domElement.clientHeight) : this.enablePan && this._pan(this.keyPanSpeed, 0), t = !0;
        break;
      case this.keys.RIGHT:
        e.ctrlKey || e.metaKey || e.shiftKey ? this.enableRotate && this._rotateLeft(-be * this.rotateSpeed / this.domElement.clientHeight) : this.enablePan && this._pan(-this.keyPanSpeed, 0), t = !0;
        break;
    }
    t && (e.preventDefault(), this.update());
  }
  _handleTouchStartRotate(e) {
    if (this._pointers.length === 1)
      this._rotateStart.set(e.pageX, e.pageY);
    else {
      const t = this._getSecondPointerPosition(e), s = 0.5 * (e.pageX + t.x), i = 0.5 * (e.pageY + t.y);
      this._rotateStart.set(s, i);
    }
  }
  _handleTouchStartPan(e) {
    if (this._pointers.length === 1)
      this._panStart.set(e.pageX, e.pageY);
    else {
      const t = this._getSecondPointerPosition(e), s = 0.5 * (e.pageX + t.x), i = 0.5 * (e.pageY + t.y);
      this._panStart.set(s, i);
    }
  }
  _handleTouchStartDolly(e) {
    const t = this._getSecondPointerPosition(e), s = e.pageX - t.x, i = e.pageY - t.y, a = Math.sqrt(s * s + i * i);
    this._dollyStart.set(0, a);
  }
  _handleTouchStartDollyPan(e) {
    this.enableZoom && this._handleTouchStartDolly(e), this.enablePan && this._handleTouchStartPan(e);
  }
  _handleTouchStartDollyRotate(e) {
    this.enableZoom && this._handleTouchStartDolly(e), this.enableRotate && this._handleTouchStartRotate(e);
  }
  _handleTouchMoveRotate(e) {
    if (this._pointers.length == 1)
      this._rotateEnd.set(e.pageX, e.pageY);
    else {
      const s = this._getSecondPointerPosition(e), i = 0.5 * (e.pageX + s.x), a = 0.5 * (e.pageY + s.y);
      this._rotateEnd.set(i, a);
    }
    this._rotateDelta.subVectors(this._rotateEnd, this._rotateStart).multiplyScalar(this.rotateSpeed);
    const t = this.domElement;
    this._rotateLeft(be * this._rotateDelta.x / t.clientHeight), this._rotateUp(be * this._rotateDelta.y / t.clientHeight), this._rotateStart.copy(this._rotateEnd);
  }
  _handleTouchMovePan(e) {
    if (this._pointers.length === 1)
      this._panEnd.set(e.pageX, e.pageY);
    else {
      const t = this._getSecondPointerPosition(e), s = 0.5 * (e.pageX + t.x), i = 0.5 * (e.pageY + t.y);
      this._panEnd.set(s, i);
    }
    this._panDelta.subVectors(this._panEnd, this._panStart).multiplyScalar(this.panSpeed), this._pan(this._panDelta.x, this._panDelta.y), this._panStart.copy(this._panEnd);
  }
  _handleTouchMoveDolly(e) {
    const t = this._getSecondPointerPosition(e), s = e.pageX - t.x, i = e.pageY - t.y, a = Math.sqrt(s * s + i * i);
    this._dollyEnd.set(0, a), this._dollyDelta.set(0, Math.pow(this._dollyEnd.y / this._dollyStart.y, this.zoomSpeed)), this._dollyOut(this._dollyDelta.y), this._dollyStart.copy(this._dollyEnd);
    const o = (e.pageX + t.x) * 0.5, r = (e.pageY + t.y) * 0.5;
    this._updateZoomParameters(o, r);
  }
  _handleTouchMoveDollyPan(e) {
    this.enableZoom && this._handleTouchMoveDolly(e), this.enablePan && this._handleTouchMovePan(e);
  }
  _handleTouchMoveDollyRotate(e) {
    this.enableZoom && this._handleTouchMoveDolly(e), this.enableRotate && this._handleTouchMoveRotate(e);
  }
  // pointers
  _addPointer(e) {
    this._pointers.push(e.pointerId);
  }
  _removePointer(e) {
    delete this._pointerPositions[e.pointerId];
    for (let t = 0; t < this._pointers.length; t++)
      if (this._pointers[t] == e.pointerId) {
        this._pointers.splice(t, 1);
        return;
      }
  }
  _isTrackingPointer(e) {
    for (let t = 0; t < this._pointers.length; t++)
      if (this._pointers[t] == e.pointerId) return !0;
    return !1;
  }
  _trackPointer(e) {
    let t = this._pointerPositions[e.pointerId];
    t === void 0 && (t = new ye(), this._pointerPositions[e.pointerId] = t), t.set(e.pageX, e.pageY);
  }
  _getSecondPointerPosition(e) {
    const t = e.pointerId === this._pointers[0] ? this._pointers[1] : this._pointers[0];
    return this._pointerPositions[t];
  }
  //
  _customWheelEvent(e) {
    const t = e.deltaMode, s = {
      clientX: e.clientX,
      clientY: e.clientY,
      deltaY: e.deltaY
    };
    switch (t) {
      case 1:
        s.deltaY *= 16;
        break;
      case 2:
        s.deltaY *= 100;
        break;
    }
    return e.ctrlKey && !this._controlActive && (s.deltaY *= 10), s;
  }
}
function QA(n) {
  this.enabled !== !1 && (this._pointers.length === 0 && (this.domElement.setPointerCapture(n.pointerId), this.domElement.addEventListener("pointermove", this._onPointerMove), this.domElement.addEventListener("pointerup", this._onPointerUp)), !this._isTrackingPointer(n) && (this._addPointer(n), n.pointerType === "touch" ? this._onTouchStart(n) : this._onMouseDown(n)));
}
function BA(n) {
  this.enabled !== !1 && (n.pointerType === "touch" ? this._onTouchMove(n) : this._onMouseMove(n));
}
function DA(n) {
  switch (this._removePointer(n), this._pointers.length) {
    case 0:
      this.domElement.releasePointerCapture(n.pointerId), this.domElement.removeEventListener("pointermove", this._onPointerMove), this.domElement.removeEventListener("pointerup", this._onPointerUp), this.dispatchEvent(yr), this.state = oe.NONE;
      break;
    case 1:
      const e = this._pointers[0], t = this._pointerPositions[e];
      this._onTouchStart({ pointerId: e, pageX: t.x, pageY: t.y });
      break;
  }
}
function kA(n) {
  let e;
  switch (n.button) {
    case 0:
      e = this.mouseButtons.LEFT;
      break;
    case 1:
      e = this.mouseButtons.MIDDLE;
      break;
    case 2:
      e = this.mouseButtons.RIGHT;
      break;
    default:
      e = -1;
  }
  switch (e) {
    case qe.DOLLY:
      if (this.enableZoom === !1) return;
      this._handleMouseDownDolly(n), this.state = oe.DOLLY;
      break;
    case qe.ROTATE:
      if (n.ctrlKey || n.metaKey || n.shiftKey) {
        if (this.enablePan === !1) return;
        this._handleMouseDownPan(n), this.state = oe.PAN;
      } else {
        if (this.enableRotate === !1) return;
        this._handleMouseDownRotate(n), this.state = oe.ROTATE;
      }
      break;
    case qe.PAN:
      if (n.ctrlKey || n.metaKey || n.shiftKey) {
        if (this.enableRotate === !1) return;
        this._handleMouseDownRotate(n), this.state = oe.ROTATE;
      } else {
        if (this.enablePan === !1) return;
        this._handleMouseDownPan(n), this.state = oe.PAN;
      }
      break;
    default:
      this.state = oe.NONE;
  }
  this.state !== oe.NONE && this.dispatchEvent(Ui);
}
function TA(n) {
  switch (this.state) {
    case oe.ROTATE:
      if (this.enableRotate === !1) return;
      this._handleMouseMoveRotate(n);
      break;
    case oe.DOLLY:
      if (this.enableZoom === !1) return;
      this._handleMouseMoveDolly(n);
      break;
    case oe.PAN:
      if (this.enablePan === !1) return;
      this._handleMouseMovePan(n);
      break;
  }
}
function RA(n) {
  this.enabled === !1 || this.enableZoom === !1 || this.state !== oe.NONE || (n.preventDefault(), this.dispatchEvent(Ui), this._handleMouseWheel(this._customWheelEvent(n)), this.dispatchEvent(yr));
}
function LA(n) {
  this.enabled !== !1 && this._handleKeyDown(n);
}
function FA(n) {
  switch (this._trackPointer(n), this._pointers.length) {
    case 1:
      switch (this.touches.ONE) {
        case st.ROTATE:
          if (this.enableRotate === !1) return;
          this._handleTouchStartRotate(n), this.state = oe.TOUCH_ROTATE;
          break;
        case st.PAN:
          if (this.enablePan === !1) return;
          this._handleTouchStartPan(n), this.state = oe.TOUCH_PAN;
          break;
        default:
          this.state = oe.NONE;
      }
      break;
    case 2:
      switch (this.touches.TWO) {
        case st.DOLLY_PAN:
          if (this.enableZoom === !1 && this.enablePan === !1) return;
          this._handleTouchStartDollyPan(n), this.state = oe.TOUCH_DOLLY_PAN;
          break;
        case st.DOLLY_ROTATE:
          if (this.enableZoom === !1 && this.enableRotate === !1) return;
          this._handleTouchStartDollyRotate(n), this.state = oe.TOUCH_DOLLY_ROTATE;
          break;
        default:
          this.state = oe.NONE;
      }
      break;
    default:
      this.state = oe.NONE;
  }
  this.state !== oe.NONE && this.dispatchEvent(Ui);
}
function PA(n) {
  switch (this._trackPointer(n), this.state) {
    case oe.TOUCH_ROTATE:
      if (this.enableRotate === !1) return;
      this._handleTouchMoveRotate(n), this.update();
      break;
    case oe.TOUCH_PAN:
      if (this.enablePan === !1) return;
      this._handleTouchMovePan(n), this.update();
      break;
    case oe.TOUCH_DOLLY_PAN:
      if (this.enableZoom === !1 && this.enablePan === !1) return;
      this._handleTouchMoveDollyPan(n), this.update();
      break;
    case oe.TOUCH_DOLLY_ROTATE:
      if (this.enableZoom === !1 && this.enableRotate === !1) return;
      this._handleTouchMoveDollyRotate(n), this.update();
      break;
    default:
      this.state = oe.NONE;
  }
}
function _A(n) {
  this.enabled !== !1 && n.preventDefault();
}
function UA(n) {
  n.key === "Control" && (this._controlActive = !0, this.domElement.getRootNode().addEventListener("keyup", this._interceptControlUp, { passive: !0, capture: !0 }));
}
function NA(n) {
  n.key === "Control" && (this._controlActive = !1, this.domElement.getRootNode().removeEventListener("keyup", this._interceptControlUp, { passive: !0, capture: !0 }));
}
class WA extends Ir {
  constructor(e) {
    const t = e.camera;
    super(t, e.renderer.domElement);
    const s = this;
    s.dampingFactor = 0.1, s.rotateSpeed = 0.4, s.autoRotateSpeed = 0.5, s.updateByOptions(e);
  }
  updateByOptions(e = {}) {
    if (!e) return;
    const t = this;
    e.enableDamping !== void 0 && (t.enableDamping = e.enableDamping), e.autoRotate !== void 0 && (t.autoRotate = e.autoRotate, t.autoRotate), e.autoRotateSpeed !== void 0 && (t.autoRotateSpeed = e.autoRotateSpeed, t.autoRotateSpeed), e.enableZoom !== void 0 && (t.enableZoom = e.enableZoom), e.enableRotate !== void 0 && (t.enableRotate = e.enableRotate), e.enablePan !== void 0 && (t.enablePan = e.enablePan), e.minDistance !== void 0 && (t.minDistance = e.minDistance), e.maxDistance !== void 0 && (t.maxDistance = e.maxDistance), e.minPolarAngle !== void 0 && (t.minPolarAngle = e.minPolarAngle), e.maxPolarAngle !== void 0 && (t.maxPolarAngle = e.maxPolarAngle), e.fov !== void 0 && (t.object.fov = e.fov), e.near !== void 0 && (t.object.near = e.near), e.far !== void 0 && (t.object.far = e.far), e.position && t.object.position.fromArray(e.position), e.lookAt && t.target.fromArray(e.lookAt), e.lookUp && t.object.up.fromArray(e.lookUp), t.updateControlMode(e), Ce && t._dollyOut?.(0.75), t.updateRotateAxis(), t.update();
  }
  /**
   * 更新控制模式
   * @param opts 选项
   */
  updateControlMode(e) {
    const t = this, s = e.useCustomControl === !0;
    if (t.object.position.toArray(), t.target.toArray(), s) {
      t.disconnect(), t.enabled = !1, t.enableRotate = !1, t.enablePan = !1, t.enableZoom = !1, t.enableDamping = !1, t.autoRotate = !1;
      const i = this.getDistance();
      t.minDistance = i, t.maxDistance = i;
    } else
      t.enabled = !0, t.enableRotate = e.enableRotate !== void 0 ? e.enableRotate : !0, t.enablePan = e.enablePan !== void 0 ? e.enablePan : !0, t.enableZoom = e.enableZoom !== void 0 ? e.enableZoom : !0, t.enableDamping = e.enableDamping !== void 0 ? e.enableDamping : !0, t.autoRotate = e.autoRotate !== void 0 ? e.autoRotate : !1, t.autoRotate, t.minDistance = e.minDistance !== void 0 ? e.minDistance : 0, t.maxDistance = e.maxDistance !== void 0 ? e.maxDistance : 1 / 0, t.minPolarAngle = e.minPolarAngle !== void 0 ? e.minPolarAngle : 0, t.maxPolarAngle = e.maxPolarAngle !== void 0 ? e.maxPolarAngle : Math.PI / 2, t.connect();
    t.object.position.toArray(), t.target.toArray();
  }
  /**
   * 获取相机到目标的距离
   */
  getDistance() {
    return this._spherical?.radius || 1;
  }
  /**
   * 更新旋转轴
   */
  updateRotateAxis() {
    this._quat?.setFromUnitVectors?.(this.object.up, new k(0, 1, 0)), this._quatInverse = this._quat?.clone?.()?.invert?.();
  }
  /**
   * 覆盖OrbitControls的update方法，在第一人称模式下防止相机位置变化
   * @returns 是否进行了更新
   */
  update() {
    return this.enabled === !1 ? !1 : super.update();
  }
  /**
   * 重新连接OrbitControls的事件监听器
   * 这个方法在从第一人称模式切换回轨道控制模式时调用
   */
  connect() {
    super.connect();
  }
  /**
   * 断开OrbitControls的事件监听器
   * 这个方法在切换到第一人称模式时调用
   */
  disconnect() {
    super.disconnect();
  }
}
class HA extends Ir {
  constructor(e, t) {
    super(e, t), this.screenSpacePanning = !1, this.mouseButtons = { LEFT: qe.PAN, MIDDLE: qe.DOLLY, RIGHT: qe.ROTATE }, this.touches = { ONE: st.PAN, TWO: st.DOLLY_ROTATE };
  }
}
const qo = new Ii(), Bn = new k();
class wr extends zs {
  constructor() {
    super(), this.isLineSegmentsGeometry = !0, this.type = "LineSegmentsGeometry";
    const e = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0], t = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2], s = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5];
    this.setIndex(s), this.setAttribute("position", new Ze(e, 3)), this.setAttribute("uv", new Ze(t, 2));
  }
  applyMatrix4(e) {
    const t = this.attributes.instanceStart, s = this.attributes.instanceEnd;
    return t !== void 0 && (t.applyMatrix4(e), s.applyMatrix4(e), t.needsUpdate = !0), this.boundingBox !== null && this.computeBoundingBox(), this.boundingSphere !== null && this.computeBoundingSphere(), this;
  }
  setPositions(e) {
    let t;
    e instanceof Float32Array ? t = e : Array.isArray(e) && (t = new Float32Array(e));
    const s = new Os(t, 6, 1);
    return this.setAttribute("instanceStart", new Qt(s, 3, 0)), this.setAttribute("instanceEnd", new Qt(s, 3, 3)), this.instanceCount = this.attributes.instanceStart.count, this.computeBoundingBox(), this.computeBoundingSphere(), this;
  }
  setColors(e) {
    let t;
    e instanceof Float32Array ? t = e : Array.isArray(e) && (t = new Float32Array(e));
    const s = new Os(t, 6, 1);
    return this.setAttribute("instanceColorStart", new Qt(s, 3, 0)), this.setAttribute("instanceColorEnd", new Qt(s, 3, 3)), this;
  }
  fromWireframeGeometry(e) {
    return this.setPositions(e.attributes.position.array), this;
  }
  fromEdgesGeometry(e) {
    return this.setPositions(e.attributes.position.array), this;
  }
  fromMesh(e) {
    return this.fromWireframeGeometry(new zr(e.geometry)), this;
  }
  fromLineSegments(e) {
    const t = e.geometry;
    return this.setPositions(t.attributes.position.array), this;
  }
  computeBoundingBox() {
    this.boundingBox === null && (this.boundingBox = new Ii());
    const e = this.attributes.instanceStart, t = this.attributes.instanceEnd;
    e !== void 0 && t !== void 0 && (this.boundingBox.setFromBufferAttribute(e), qo.setFromBufferAttribute(t), this.boundingBox.union(qo));
  }
  computeBoundingSphere() {
    this.boundingSphere === null && (this.boundingSphere = new la()), this.boundingBox === null && this.computeBoundingBox();
    const e = this.attributes.instanceStart, t = this.attributes.instanceEnd;
    if (e !== void 0 && t !== void 0) {
      const s = this.boundingSphere.center;
      this.boundingBox.getCenter(s);
      let i = 0;
      for (let a = 0, o = e.count; a < o; a++)
        Bn.fromBufferAttribute(e, a), i = Math.max(i, s.distanceToSquared(Bn)), Bn.fromBufferAttribute(t, a), i = Math.max(i, s.distanceToSquared(Bn));
      this.boundingSphere.radius = Math.sqrt(i), isNaN(this.boundingSphere.radius) && console.error("THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.", this);
    }
  }
  toJSON() {
  }
  applyMatrix(e) {
    return console.warn("THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4()."), this.applyMatrix4(e);
  }
}
Ln.line = {
  worldUnits: { value: 1 },
  linewidth: { value: 1 },
  resolution: { value: new ye(1, 1) },
  dashOffset: { value: 0 },
  dashScale: { value: 1 },
  dashSize: { value: 1 },
  gapSize: { value: 1 }
  // todo FIX - maybe change to totalSize
};
Rn.line = {
  uniforms: Aa.merge([
    Ln.common,
    Ln.fog,
    Ln.line
  ]),
  vertexShader: (
    /* glsl */
    `
		#include <common>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		uniform float linewidth;
		uniform vec2 resolution;

		attribute vec3 instanceStart;
		attribute vec3 instanceEnd;

		attribute vec3 instanceColorStart;
		attribute vec3 instanceColorEnd;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#ifdef USE_DASH

			uniform float dashScale;
			attribute float instanceDistanceStart;
			attribute float instanceDistanceEnd;
			varying float vLineDistance;

		#endif

		void trimSegment( const in vec4 start, inout vec4 end ) {

			// trim end segment so it terminates between the camera plane and the near plane

			// conservative estimate of the near plane
			float a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column
			float b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column
			float nearEstimate = - 0.5 * b / a;

			float alpha = ( nearEstimate - start.z ) / ( end.z - start.z );

			end.xyz = mix( start.xyz, end.xyz, alpha );

		}

		void main() {

			#ifdef USE_COLOR

				vColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;

			#endif

			#ifdef USE_DASH

				vLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;
				vUv = uv;

			#endif

			float aspect = resolution.x / resolution.y;

			// camera space
			vec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );
			vec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );

			#ifdef WORLD_UNITS

				worldStart = start.xyz;
				worldEnd = end.xyz;

			#else

				vUv = uv;

			#endif

			// special case for perspective projection, and segments that terminate either in, or behind, the camera plane
			// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space
			// but we need to perform ndc-space calculations in the shader, so we must address this issue directly
			// perhaps there is a more elegant solution -- WestLangley

			bool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column

			if ( perspective ) {

				if ( start.z < 0.0 && end.z >= 0.0 ) {

					trimSegment( start, end );

				} else if ( end.z < 0.0 && start.z >= 0.0 ) {

					trimSegment( end, start );

				}

			}

			// clip space
			vec4 clipStart = projectionMatrix * start;
			vec4 clipEnd = projectionMatrix * end;

			// ndc space
			vec3 ndcStart = clipStart.xyz / clipStart.w;
			vec3 ndcEnd = clipEnd.xyz / clipEnd.w;

			// direction
			vec2 dir = ndcEnd.xy - ndcStart.xy;

			// account for clip-space aspect ratio
			dir.x *= aspect;
			dir = normalize( dir );

			#ifdef WORLD_UNITS

				vec3 worldDir = normalize( end.xyz - start.xyz );
				vec3 tmpFwd = normalize( mix( start.xyz, end.xyz, 0.5 ) );
				vec3 worldUp = normalize( cross( worldDir, tmpFwd ) );
				vec3 worldFwd = cross( worldDir, worldUp );
				worldPos = position.y < 0.5 ? start: end;

				// height offset
				float hw = linewidth * 0.5;
				worldPos.xyz += position.x < 0.0 ? hw * worldUp : - hw * worldUp;

				// don't extend the line if we're rendering dashes because we
				// won't be rendering the endcaps
				#ifndef USE_DASH

					// cap extension
					worldPos.xyz += position.y < 0.5 ? - hw * worldDir : hw * worldDir;

					// add width to the box
					worldPos.xyz += worldFwd * hw;

					// endcaps
					if ( position.y > 1.0 || position.y < 0.0 ) {

						worldPos.xyz -= worldFwd * 2.0 * hw;

					}

				#endif

				// project the worldpos
				vec4 clip = projectionMatrix * worldPos;

				// shift the depth of the projected points so the line
				// segments overlap neatly
				vec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;
				clip.z = clipPose.z * clip.w;

			#else

				vec2 offset = vec2( dir.y, - dir.x );
				// undo aspect ratio adjustment
				dir.x /= aspect;
				offset.x /= aspect;

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				// endcaps
				if ( position.y < 0.0 ) {

					offset += - dir;

				} else if ( position.y > 1.0 ) {

					offset += dir;

				}

				// adjust for linewidth
				offset *= linewidth;

				// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...
				offset /= resolution.y;

				// select end
				vec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;

				// back to clip space
				offset *= clip.w;

				clip.xy += offset;

			#endif

			gl_Position = clip;

			vec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			#include <fog_vertex>

		}
		`
  ),
  fragmentShader: (
    /* glsl */
    `
		uniform vec3 diffuse;
		uniform float opacity;
		uniform float linewidth;

		#ifdef USE_DASH

			uniform float dashOffset;
			uniform float dashSize;
			uniform float gapSize;

		#endif

		varying float vLineDistance;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#include <common>
		#include <color_pars_fragment>
		#include <fog_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		vec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {

			float mua;
			float mub;

			vec3 p13 = p1 - p3;
			vec3 p43 = p4 - p3;

			vec3 p21 = p2 - p1;

			float d1343 = dot( p13, p43 );
			float d4321 = dot( p43, p21 );
			float d1321 = dot( p13, p21 );
			float d4343 = dot( p43, p43 );
			float d2121 = dot( p21, p21 );

			float denom = d2121 * d4343 - d4321 * d4321;

			float numer = d1343 * d4321 - d1321 * d4343;

			mua = numer / denom;
			mua = clamp( mua, 0.0, 1.0 );
			mub = ( d1343 + d4321 * ( mua ) ) / d4343;
			mub = clamp( mub, 0.0, 1.0 );

			return vec2( mua, mub );

		}

		void main() {

			#include <clipping_planes_fragment>

			#ifdef USE_DASH

				if ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps

				if ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX

			#endif

			float alpha = opacity;

			#ifdef WORLD_UNITS

				// Find the closest points on the view ray and the line segment
				vec3 rayEnd = normalize( worldPos.xyz ) * 1e5;
				vec3 lineDir = worldEnd - worldStart;
				vec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );

				vec3 p1 = worldStart + lineDir * params.x;
				vec3 p2 = rayEnd * params.y;
				vec3 delta = p1 - p2;
				float len = length( delta );
				float norm = len / linewidth;

				#ifndef USE_DASH

					#ifdef USE_ALPHA_TO_COVERAGE

						float dnorm = fwidth( norm );
						alpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );

					#else

						if ( norm > 0.5 ) {

							discard;

						}

					#endif

				#endif

			#else

				#ifdef USE_ALPHA_TO_COVERAGE

					// artifacts appear on some hardware if a derivative is taken within a conditional
					float a = vUv.x;
					float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
					float len2 = a * a + b * b;
					float dlen = fwidth( len2 );

					if ( abs( vUv.y ) > 1.0 ) {

						alpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );

					}

				#else

					if ( abs( vUv.y ) > 1.0 ) {

						float a = vUv.x;
						float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
						float len2 = a * a + b * b;

						if ( len2 > 1.0 ) discard;

					}

				#endif

			#endif

			vec4 diffuseColor = vec4( diffuse, alpha );

			#include <logdepthbuf_fragment>
			#include <color_fragment>

			gl_FragColor = vec4( diffuseColor.rgb, alpha );

			#include <tonemapping_fragment>
			#include <colorspace_fragment>
			#include <fog_fragment>
			#include <premultiplied_alpha_fragment>

		}
		`
  )
};
class Pt extends aa {
  constructor(e) {
    super({
      type: "LineMaterial",
      uniforms: Aa.clone(Rn.line.uniforms),
      vertexShader: Rn.line.vertexShader,
      fragmentShader: Rn.line.fragmentShader,
      clipping: !0
      // required for clipping support
    }), this.isLineMaterial = !0, this.setValues(e);
  }
  get color() {
    return this.uniforms.diffuse.value;
  }
  set color(e) {
    this.uniforms.diffuse.value = e;
  }
  get worldUnits() {
    return "WORLD_UNITS" in this.defines;
  }
  set worldUnits(e) {
    e === !0 ? this.defines.WORLD_UNITS = "" : delete this.defines.WORLD_UNITS;
  }
  get linewidth() {
    return this.uniforms.linewidth.value;
  }
  set linewidth(e) {
    this.uniforms.linewidth && (this.uniforms.linewidth.value = e);
  }
  get dashed() {
    return "USE_DASH" in this.defines;
  }
  set dashed(e) {
    e === !0 !== this.dashed && (this.needsUpdate = !0), e === !0 ? this.defines.USE_DASH = "" : delete this.defines.USE_DASH;
  }
  get dashScale() {
    return this.uniforms.dashScale.value;
  }
  set dashScale(e) {
    this.uniforms.dashScale.value = e;
  }
  get dashSize() {
    return this.uniforms.dashSize.value;
  }
  set dashSize(e) {
    this.uniforms.dashSize.value = e;
  }
  get dashOffset() {
    return this.uniforms.dashOffset.value;
  }
  set dashOffset(e) {
    this.uniforms.dashOffset.value = e;
  }
  get gapSize() {
    return this.uniforms.gapSize.value;
  }
  set gapSize(e) {
    this.uniforms.gapSize.value = e;
  }
  get opacity() {
    return this.uniforms.opacity.value;
  }
  set opacity(e) {
    this.uniforms && (this.uniforms.opacity.value = e);
  }
  get resolution() {
    return this.uniforms.resolution.value;
  }
  set resolution(e) {
    this.uniforms.resolution.value.copy(e);
  }
  get alphaToCoverage() {
    return "USE_ALPHA_TO_COVERAGE" in this.defines;
  }
  set alphaToCoverage(e) {
    this.defines && (e === !0 !== this.alphaToCoverage && (this.needsUpdate = !0), e === !0 ? this.defines.USE_ALPHA_TO_COVERAGE = "" : delete this.defines.USE_ALPHA_TO_COVERAGE);
  }
}
const Hs = new Xe(), $o = new k(), ea = new k(), ge = new Xe(), fe = new Xe(), Ne = new Xe(), Ys = new k(), Gs = new $e(), pe = new Vr(), ta = new k(), Dn = new Ii(), kn = new la(), We = new Xe();
let ze, Ct;
function na(n, e, t) {
  return We.set(0, 0, -e, 1).applyMatrix4(n.projectionMatrix), We.multiplyScalar(1 / We.w), We.x = Ct / t.width, We.y = Ct / t.height, We.applyMatrix4(n.projectionMatrixInverse), We.multiplyScalar(1 / We.w), Math.abs(Math.max(We.x, We.y));
}
function YA(n, e) {
  const t = n.matrixWorld, s = n.geometry, i = s.attributes.instanceStart, a = s.attributes.instanceEnd, o = Math.min(s.instanceCount, i.count);
  for (let r = 0, l = o; r < l; r++) {
    pe.start.fromBufferAttribute(i, r), pe.end.fromBufferAttribute(a, r), pe.applyMatrix4(t);
    const A = new k(), c = new k();
    ze.distanceSqToSegment(pe.start, pe.end, c, A), c.distanceTo(A) < Ct * 0.5 && e.push({
      point: c,
      pointOnLine: A,
      distance: ze.origin.distanceTo(c),
      object: n,
      face: null,
      faceIndex: r,
      uv: null,
      uv1: null
    });
  }
}
function GA(n, e, t) {
  const s = e.projectionMatrix, a = n.material.resolution, o = n.matrixWorld, r = n.geometry, l = r.attributes.instanceStart, A = r.attributes.instanceEnd, c = Math.min(r.instanceCount, l.count), d = -e.near;
  ze.at(1, Ne), Ne.w = 1, Ne.applyMatrix4(e.matrixWorldInverse), Ne.applyMatrix4(s), Ne.multiplyScalar(1 / Ne.w), Ne.x *= a.x / 2, Ne.y *= a.y / 2, Ne.z = 0, Ys.copy(Ne), Gs.multiplyMatrices(e.matrixWorldInverse, o);
  for (let h = 0, p = c; h < p; h++) {
    if (ge.fromBufferAttribute(l, h), fe.fromBufferAttribute(A, h), ge.w = 1, fe.w = 1, ge.applyMatrix4(Gs), fe.applyMatrix4(Gs), ge.z > d && fe.z > d)
      continue;
    if (ge.z > d) {
      const v = ge.z - fe.z, x = (ge.z - d) / v;
      ge.lerp(fe, x);
    } else if (fe.z > d) {
      const v = fe.z - ge.z, x = (fe.z - d) / v;
      fe.lerp(ge, x);
    }
    ge.applyMatrix4(s), fe.applyMatrix4(s), ge.multiplyScalar(1 / ge.w), fe.multiplyScalar(1 / fe.w), ge.x *= a.x / 2, ge.y *= a.y / 2, fe.x *= a.x / 2, fe.y *= a.y / 2, pe.start.copy(ge), pe.start.z = 0, pe.end.copy(fe), pe.end.z = 0;
    const y = pe.closestPointToPointParameter(Ys, !0);
    pe.at(y, ta);
    const g = it.lerp(ge.z, fe.z, y), f = g >= -1 && g <= 1, u = Ys.distanceTo(ta) < Ct * 0.5;
    if (f && u) {
      pe.start.fromBufferAttribute(l, h), pe.end.fromBufferAttribute(A, h), pe.start.applyMatrix4(o), pe.end.applyMatrix4(o);
      const v = new k(), x = new k();
      ze.distanceSqToSegment(pe.start, pe.end, x, v), t.push({
        point: x,
        pointOnLine: v,
        distance: ze.origin.distanceTo(x),
        object: n,
        face: null,
        faceIndex: h,
        uv: null,
        uv1: null
      });
    }
  }
}
class zA extends Ie {
  constructor(e = new wr(), t = new Pt({ color: Math.random() * 16777215 })) {
    super(e, t), this.isLineSegments2 = !0, this.type = "LineSegments2";
  }
  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...
  computeLineDistances() {
    const e = this.geometry, t = e.attributes.instanceStart, s = e.attributes.instanceEnd, i = new Float32Array(2 * t.count);
    for (let o = 0, r = 0, l = t.count; o < l; o++, r += 2)
      $o.fromBufferAttribute(t, o), ea.fromBufferAttribute(s, o), i[r] = r === 0 ? 0 : i[r - 1], i[r + 1] = i[r] + $o.distanceTo(ea);
    const a = new Os(i, 2, 1);
    return e.setAttribute("instanceDistanceStart", new Qt(a, 1, 0)), e.setAttribute("instanceDistanceEnd", new Qt(a, 1, 1)), this;
  }
  raycast(e, t) {
    const s = this.material.worldUnits, i = e.camera;
    i === null && !s && console.error('LineSegments2: "Raycaster.camera" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.');
    const a = e.params.Line2 !== void 0 && e.params.Line2.threshold || 0;
    ze = e.ray;
    const o = this.matrixWorld, r = this.geometry, l = this.material;
    Ct = l.linewidth + a, r.boundingSphere === null && r.computeBoundingSphere(), kn.copy(r.boundingSphere).applyMatrix4(o);
    let A;
    if (s)
      A = Ct * 0.5;
    else {
      const d = Math.max(i.near, kn.distanceToPoint(ze.origin));
      A = na(i, d, l.resolution);
    }
    if (kn.radius += A, ze.intersectsSphere(kn) === !1)
      return;
    r.boundingBox === null && r.computeBoundingBox(), Dn.copy(r.boundingBox).applyMatrix4(o);
    let c;
    if (s)
      c = Ct * 0.5;
    else {
      const d = Math.max(i.near, Dn.distanceToPoint(ze.origin));
      c = na(i, d, l.resolution);
    }
    Dn.expandByScalar(c), ze.intersectsBox(Dn) !== !1 && (s ? YA(this, t) : GA(this, i, t));
  }
  onBeforeRender(e) {
    const t = this.material.uniforms;
    t && t.resolution && (e.getViewport(Hs), this.material.uniforms.resolution.value.set(Hs.z, Hs.w));
  }
}
class sn extends wr {
  constructor() {
    super(), this.isLineGeometry = !0, this.type = "LineGeometry";
  }
  setPositions(e) {
    const t = e.length - 3, s = new Float32Array(2 * t);
    for (let i = 0; i < t; i += 3)
      s[2 * i] = e[i], s[2 * i + 1] = e[i + 1], s[2 * i + 2] = e[i + 2], s[2 * i + 3] = e[i + 3], s[2 * i + 4] = e[i + 4], s[2 * i + 5] = e[i + 5];
    return super.setPositions(s), this;
  }
  setColors(e) {
    const t = e.length - 3, s = new Float32Array(2 * t);
    for (let i = 0; i < t; i += 3)
      s[2 * i] = e[i], s[2 * i + 1] = e[i + 1], s[2 * i + 2] = e[i + 2], s[2 * i + 3] = e[i + 3], s[2 * i + 4] = e[i + 4], s[2 * i + 5] = e[i + 5];
    return super.setColors(s), this;
  }
  setFromPoints(e) {
    const t = e.length - 1, s = new Float32Array(6 * t);
    for (let i = 0; i < t; i++)
      s[6 * i] = e[i].x, s[6 * i + 1] = e[i].y, s[6 * i + 2] = e[i].z || 0, s[6 * i + 3] = e[i + 1].x, s[6 * i + 4] = e[i + 1].y, s[6 * i + 5] = e[i + 1].z || 0;
    return super.setPositions(s), this;
  }
  fromLine(e) {
    const t = e.geometry;
    return this.setPositions(t.attributes.position.array), this;
  }
}
class yt extends zA {
  constructor(e = new sn(), t = new Pt({ color: Math.random() * 16777215 })) {
    super(e, t), this.isLine2 = !0, this.type = "Line2";
  }
}
const sa = new k(), VA = new rt(), ia = new k();
class OA extends is {
  constructor(e = document.createElement("div")) {
    super(), this.isCSS3DObject = !0, this.element = e, this.element.style.position = "absolute", this.element.style.pointerEvents = "auto", this.element.style.userSelect = "none", this.element.setAttribute("draggable", !1), this.addEventListener("removed", function() {
      this.traverse(function(t) {
        t.element instanceof t.element.ownerDocument.defaultView.Element && t.element.parentNode !== null && t.element.remove();
      });
    });
  }
  copy(e, t) {
    return super.copy(e, t), this.element = e.element.cloneNode(!0), this;
  }
}
class we extends OA {
  constructor(e) {
    super(e), this.isCSS3DSprite = !0, this.rotation2D = 0;
  }
  copy(e, t) {
    return super.copy(e, t), this.rotation2D = e.rotation2D, this;
  }
}
const He = new $e(), XA = new $e();
class JA {
  constructor(e = {}) {
    const t = this;
    let s, i, a, o;
    const r = {
      camera: { style: "" },
      objects: /* @__PURE__ */ new WeakMap()
    }, l = e.element !== void 0 ? e.element : document.createElement("div");
    l.style.overflow = "hidden", this.domElement = l;
    const A = document.createElement("div");
    A.style.transformOrigin = "0 0", A.style.pointerEvents = "none", l.appendChild(A);
    const c = document.createElement("div");
    c.style.transformStyle = "preserve-3d", A.appendChild(c), this.getSize = function() {
      return {
        width: s,
        height: i
      };
    }, this.render = function(g, f) {
      const u = f.projectionMatrix.elements[5] * o;
      f.view && f.view.enabled ? (A.style.transform = `translate( ${-f.view.offsetX * (s / f.view.width)}px, ${-f.view.offsetY * (i / f.view.height)}px )`, A.style.transform += `scale( ${f.view.fullWidth / f.view.width}, ${f.view.fullHeight / f.view.height} )`) : A.style.transform = "", g.matrixWorldAutoUpdate === !0 && g.updateMatrixWorld(), f.parent === null && f.matrixWorldAutoUpdate === !0 && f.updateMatrixWorld();
      let v, x;
      f.isOrthographicCamera && (v = -(f.right + f.left) / 2, x = (f.top + f.bottom) / 2);
      const R = f.view && f.view.enabled ? f.view.height / f.view.fullHeight : 1, M = f.isOrthographicCamera ? `scale( ${R} )scale(` + u + ")translate(" + d(v) + "px," + d(x) + "px)" + h(f.matrixWorldInverse) : `scale( ${R} )translateZ(` + u + "px)" + h(f.matrixWorldInverse), S = (f.isPerspectiveCamera ? "perspective(" + u + "px) " : "") + M + "translate(" + a + "px," + o + "px)";
      r.camera.style !== S && (c.style.transform = S, r.camera.style = S), y(g, g, f);
    }, this.setSize = function(g, f) {
      s = g, i = f, a = s / 2, o = i / 2, l.style.width = g + "px", l.style.height = f + "px", A.style.width = g + "px", A.style.height = f + "px", c.style.width = g + "px", c.style.height = f + "px";
    };
    function d(g) {
      return Math.abs(g) < 1e-10 ? 0 : g;
    }
    function h(g) {
      const f = g.elements;
      return "matrix3d(" + d(f[0]) + "," + d(-f[1]) + "," + d(f[2]) + "," + d(f[3]) + "," + d(f[4]) + "," + d(-f[5]) + "," + d(f[6]) + "," + d(f[7]) + "," + d(f[8]) + "," + d(-f[9]) + "," + d(f[10]) + "," + d(f[11]) + "," + d(f[12]) + "," + d(-f[13]) + "," + d(f[14]) + "," + d(f[15]) + ")";
    }
    function p(g) {
      const f = g.elements;
      return "translate(-50%,-50%)" + ("matrix3d(" + d(f[0]) + "," + d(f[1]) + "," + d(f[2]) + "," + d(f[3]) + "," + d(-f[4]) + "," + d(-f[5]) + "," + d(-f[6]) + "," + d(-f[7]) + "," + d(f[8]) + "," + d(f[9]) + "," + d(f[10]) + "," + d(f[11]) + "," + d(f[12]) + "," + d(f[13]) + "," + d(f[14]) + "," + d(f[15]) + ")");
    }
    function m(g) {
      g.isCSS3DObject && (g.element.style.display = "none");
      for (let f = 0, u = g.children.length; f < u; f++)
        m(g.children[f]);
    }
    function y(g, f, u, v) {
      if (g.visible === !1) {
        m(g);
        return;
      }
      if (g.isCSS3DObject) {
        const x = g.layers.test(u.layers) === !0, R = g.element;
        if (R.style.display = x === !0 ? "" : "none", x === !0) {
          g.onBeforeRender(t, f, u);
          let M;
          g.isCSS3DSprite ? (He.copy(u.matrixWorldInverse), He.transpose(), g.rotation2D !== 0 && He.multiply(XA.makeRotationZ(g.rotation2D)), g.matrixWorld.decompose(sa, VA, ia), He.setPosition(sa), He.scale(ia), He.elements[3] = 0, He.elements[7] = 0, He.elements[11] = 0, He.elements[15] = 1, M = p(He)) : M = p(g.matrixWorld);
          const D = r.objects.get(g);
          if (D === void 0 || D.style !== M) {
            R.style.transform = M;
            const S = { style: M };
            r.objects.set(g, S);
          }
          R.parentNode !== c && c.appendChild(R), g.onAfterRender(t, f, u);
        }
      }
      for (let x = 0, R = g.children.length; x < R; x++)
        y(g.children[x], f, u);
    }
  }
}
class gi extends yt {
  constructor(e) {
    super(), this.isMark = !0, this.disposed = !1, this.events = e;
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return;
    const s = this, i = document.querySelectorAll(".mark-wrap-line.main-warp").length + 1, a = {
      type: "MarkDistanceLine",
      name: t || "line" + Date.now(),
      startPoint: e.toArray(),
      endPoint: e.toArray(),
      lineColor: "#eeee00",
      lineWidth: 3,
      mainTagColor: "#c4c4c4",
      mainTagBackground: "#2E2E30",
      mainTagOpacity: 0.8,
      mainTagVisible: !0,
      distanceTagColor: "#000000",
      distanceTagBackground: "#e0ffff",
      distanceTagOpacity: 0.9,
      distanceTagVisible: !0,
      title: "标记距离" + i
    }, o = new sn();
    o.setPositions([...a.startPoint, ...a.endPoint]);
    const r = new Pt({ color: a.lineColor, linewidth: a.lineWidth });
    r.resolution.set(innerWidth, innerHeight), s.copy(new yt(o, r));
    const l = new gt(0.05, 32), A = new at({ color: 16777215, side: et });
    A.transparent = !0, A.opacity = 0.6;
    const c = new Ie(l, A);
    c.position.copy(e), c.isMark = !0;
    const d = new Ie(l, A);
    d.position.copy(e), d.isMark = !0;
    const h = document.createElement("div");
    h.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`, h.classList.add("mark-wrap-line", `${a.name}`, "main-warp"), h.style.position = "absolute", h.style.borderRadius = "4px", h.style.cursor = "pointer", h.onclick = () => {
      if (s.events.fire(F).markMode) return;
      const g = parent?.onActiveMark;
      g?.(s.getMarkData(!0)), s.events.fire(me);
    }, h.oncontextmenu = (g) => g.preventDefault();
    const p = new we(h);
    p.position.copy(e), p.element.style.pointerEvents = "none", p.scale.set(0.01, 0.01, 0.01), p.visible = a.mainTagVisible;
    const m = document.createElement("div");
    m.innerHTML = `<span class="${t}-distance-tag ${t}-distance-tag0" style="color:${a.distanceTagColor};background:${a.distanceTagBackground};opacity:${a.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`, m.classList.add("mark-wrap-line", `${t}`, "distance-warp"), m.style.position = "absolute", m.style.borderRadius = "4px", m.style.pointerEvents = "none";
    const y = new we(m);
    y.position.set(e.x, e.y, e.z), y.element.style.pointerEvents = "none", y.scale.set(8e-3, 8e-3, 8e-3), y.visible = !1, s.add(c, d, p, y), s.data = a, s.circleStart = c, s.circleEnd = d, s.css3dTag = y, s.css3dMainTag = p, s.events.fire(ct, s);
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0) {
    if (this.disposed) return;
    const s = this;
    if (e?.endPoint) {
      t && (s.data.endPoint = [...e.endPoint]);
      const i = new k().fromArray(s.data.startPoint), a = new k().fromArray(e.endPoint);
      s.geometry.setPositions([...s.data.startPoint, ...e.endPoint]);
      const o = new k((i.x + a.x) / 2, (i.y + a.y) / 2, (i.z + a.z) / 2);
      s.css3dTag.position.set(o.x, o.y, o.z);
      const r = a.clone().sub(i).normalize();
      s.circleStart.lookAt(s.circleStart.position.clone().add(r)), s.circleEnd.lookAt(s.circleEnd.position.clone().add(r));
      const l = i.distanceTo(a);
      s.css3dTag.visible = l > 0.5;
      const A = (l * s.events.fire(F).meterScale).toFixed(2) + " m";
      s.css3dTag.element.childNodes[0].innerText = A;
    }
    e?.lineColor && (t && (s.data.lineColor = e.lineColor), s.material.color.set(e.lineColor)), e?.lineWidth && (t && (s.data.lineWidth = e.lineWidth), s.material.linewidth = e.lineWidth), e?.mainTagColor && (t && (s.data.mainTagColor = e.mainTagColor), document.querySelector(`.${s.data.name}-main-tag`).style.color = e.mainTagColor), e?.mainTagBackground && (t && (s.data.mainTagBackground = e.mainTagBackground), document.querySelector(`.${s.data.name}-main-tag`).style.background = e.mainTagBackground), e?.mainTagOpacity && (t && (s.data.mainTagOpacity = e.mainTagOpacity), document.querySelector(`.${s.data.name}-main-tag`).style.opacity = e.mainTagOpacity.toString()), e?.mainTagVisible !== void 0 && (t && (s.data.mainTagVisible = e.mainTagVisible), s.css3dMainTag.visible = e.mainTagVisible), e?.distanceTagColor && (t && (s.data.distanceTagColor = e.distanceTagColor), document.querySelector(`.${s.data.name}-distance-tag0`).style.color = e.distanceTagColor), e?.distanceTagBackground && (t && (s.data.distanceTagBackground = e.distanceTagBackground), document.querySelector(`.${s.data.name}-distance-tag0`).style.background = e.distanceTagBackground), e?.distanceTagOpacity && (t && (s.data.distanceTagOpacity = e.distanceTagOpacity), document.querySelector(`.${s.data.name}-distance-tag0`).style.opacity = e.distanceTagOpacity.toString()), e?.distanceTagVisible !== void 0 && (t && (s.data.distanceTagVisible = e.distanceTagVisible), s.css3dTag.visible = e.distanceTagVisible), e?.title !== void 0 && (t && (s.data.title = e.title), this.css3dMainTag.element.querySelector(`.${s.data.name}-main-tag`).innerText = e.title), e?.note !== void 0 && t && (s.data.note = e.note), s.events.fire(j);
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this, s = new k().fromArray(t.data.startPoint), i = new k().fromArray(t.data.endPoint), a = (s.distanceTo(i) * e).toFixed(2) + " m";
    t.css3dTag.element.childNodes[0].innerText = a;
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return;
    const t = this;
    t.data.endPoint = [...e.toArray()];
    const s = new k().fromArray(t.data.startPoint), i = new k().fromArray(t.data.endPoint), a = new k((s.x + i.x) / 2, (s.y + i.y) / 2, (s.z + i.z) / 2);
    t.geometry.setPositions([...t.data.startPoint, ...t.data.endPoint]), t.css3dTag.position.set(a.x, a.y, a.z);
    const o = i.clone().sub(s).normalize();
    t.circleStart.lookAt(t.circleStart.position.clone().add(o)), t.circleEnd.lookAt(t.circleEnd.position.clone().add(o)), t.circleEnd.position.copy(i);
    const r = s.distanceTo(i);
    t.css3dTag.visible = !0;
    const l = (r * t.events.fire(F).meterScale).toFixed(2) + " m";
    t.css3dTag.element.childNodes[0].innerText = l, t.events.fire(lt);
    const A = parent?.onActiveMark, c = t.getMarkData(!0);
    c.isNew = !0, c.meterScale = t.events.fire(F).meterScale, A?.(c);
  }
  /**
   * 根据数据直接绘制
   */
  draw(e) {
    if (this.disposed) return;
    const t = this, s = {
      type: "MarkDistanceLine",
      name: e.name || "line" + Date.now(),
      startPoint: [...e.startPoint],
      endPoint: [...e.endPoint],
      lineColor: e.lineColor || "#eeee00",
      lineWidth: e.lineWidth || 3,
      mainTagColor: e.mainTagColor || "#c4c4c4",
      mainTagBackground: e.mainTagBackground || "#2E2E30",
      mainTagOpacity: e.mainTagOpacity || 0.8,
      mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
      distanceTagColor: e.distanceTagColor || "#000000",
      distanceTagBackground: e.distanceTagBackground || "#e0ffff",
      distanceTagOpacity: e.distanceTagOpacity || 0.9,
      distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
      title: e.title || "标记距离"
    }, i = new k().fromArray(s.startPoint), a = new k().fromArray(s.endPoint), o = new k((i.x + a.x) / 2, (i.y + a.y) / 2, (i.z + a.z) / 2), r = new sn();
    r.setPositions([...s.startPoint, ...s.endPoint]);
    const l = new Pt({ color: s.lineColor, linewidth: s.lineWidth });
    l.resolution.set(innerWidth, innerHeight), t.copy(new yt(r, l));
    const A = new gt(0.05, 32), c = new at({ color: 16777215, side: et });
    c.transparent = !0, c.opacity = 0.6;
    const d = new Ie(A, c);
    d.position.copy(i), d.isMark = !0;
    const h = new Ie(A, c);
    h.position.copy(a), h.isMark = !0;
    const p = a.clone().sub(i).normalize();
    d.lookAt(d.position.clone().add(p)), h.lookAt(h.position.clone().add(p));
    const m = s.name, y = document.createElement("div");
    y.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${m}-main-tag" style="color:${s.mainTagColor};background:${s.mainTagBackground};opacity:${s.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${s.title}</span>
                             </div>`, y.classList.add("mark-wrap-line", `${s.name}`, "main-warp"), y.style.position = "absolute", y.style.borderRadius = "4px", y.style.cursor = "pointer", y.onclick = () => {
      if (t.events.fire(F).markMode) return;
      const R = parent?.onActiveMark;
      R?.(t.getMarkData(!0)), t.events.fire(me);
    }, y.oncontextmenu = (R) => R.preventDefault();
    const g = new we(y);
    g.position.copy(i), g.element.style.pointerEvents = "none", g.scale.set(0.01, 0.01, 0.01), g.visible = s.mainTagVisible;
    const u = (i.distanceTo(a) * t.events.fire(F).meterScale).toFixed(2) + " m", v = document.createElement("div");
    v.innerHTML = `<span class="${m}-distance-tag ${m}-distance-tag0" style="color:${s.distanceTagColor};background:${s.distanceTagBackground};opacity:${s.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${u}</span>`, v.classList.add("mark-wrap-line", `${m}`, "distance-warp"), v.style.position = "absolute", v.style.borderRadius = "4px", v.style.pointerEvents = "none";
    const x = new we(v);
    x.position.copy(o), x.element.style.pointerEvents = "none", x.scale.set(8e-3, 8e-3, 8e-3), x.visible = s.distanceTagVisible, t.add(d, h, g, x), t.data = s, t.circleStart = d, t.circleEnd = h, t.css3dTag = x, t.css3dMainTag = g, t.events.fire(ct, t);
  }
  getMarkData(e = !1) {
    const t = { ...this.data };
    return e ? (delete t.startPoint, delete t.endPoint) : (t.startPoint = [...t.startPoint], t.endPoint = [...t.endPoint]), t;
  }
  dispose() {
    if (this.disposed) return;
    const e = this;
    e.disposed = !0, e.events.fire(tt, e), e.events.fire(V).remove(e), e.events.fire(Ut, e), document.querySelectorAll(`.${e.data.name}`).forEach((s) => s.parentElement?.removeChild(s)), e.events = null, e.data = null, e.circleStart = null, e.circleEnd = null, e.css3dTag = null, e.css3dMainTag = null;
  }
}
class fi extends yt {
  constructor(e) {
    super(), this.isMark = !0, this.disposed = !1, this.css3dTags = [], this.group = new on(), this.add(this.group), this.events = e;
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return;
    const s = document.querySelectorAll(".mark-wrap-lines.main-warp").length + 1, i = {
      type: "MarkMultiLines",
      name: t || "lines" + Date.now(),
      points: [...e.toArray(), ...e.toArray()],
      lineColor: "#eeee00",
      lineWidth: 3,
      mainTagColor: "#c4c4c4",
      mainTagBackground: "#2E2E30",
      mainTagOpacity: 0.8,
      mainTagVisible: !0,
      distanceTagColor: "#000000",
      distanceTagBackground: "#e0ffff",
      distanceTagOpacity: 0.9,
      distanceTagVisible: !0,
      title: "标记线" + s,
      note: ""
    };
    this.draw(i);
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, s, i = !1) {
    if (this.disposed) return;
    const a = this;
    if (s)
      if (i) {
        const o = a.data.points.length, r = a.css3dTags.length - 1, l = new k().fromArray(a.data.points.slice(o - 6, o - 3)), A = s, c = new k((l.x + A.x) / 2, (l.y + A.y) / 2, (l.z + A.z) / 2), h = (l.distanceTo(A) * a.events.fire(F).meterScale).toFixed(2) + " m";
        a.css3dTags[r].element.childNodes[0].innerText = h, a.css3dTags[r].position.set(c.x, c.y, c.z), a.css3dTags[r].visible = !0, a.data.points.pop(), a.data.points.pop(), a.data.points.pop(), a.data.points = [...a.data.points, ...s.toArray(), ...s.toArray()], a.css3dTags[a.css3dTags.length - 1].visible = !0, this.draw(this.data);
      } else {
        const o = a.data.points.length;
        a.data.points[o - 3] = s.x, a.data.points[o - 2] = s.y, a.data.points[o - 1] = s.z;
        const r = a.css3dTags.length - 1, l = new k().fromArray(a.data.points.slice(o - 6, o - 3)), A = new k().fromArray(a.data.points.slice(o - 3)), c = new k((l.x + A.x) / 2, (l.y + A.y) / 2, (l.z + A.z) / 2), d = l.distanceTo(A), h = (d * a.events.fire(F).meterScale).toFixed(2) + " m";
        a.css3dTags[r].element.childNodes[0].innerText = h, a.geometry.setPositions([...a.data.points]), a.css3dTags[r].position.set(c.x, c.y, c.z), a.css3dTags[r].visible = i ? !0 : d > 0.5;
      }
    e?.lineColor && (t && (a.data.lineColor = e.lineColor), a.material.color.set(e.lineColor)), e?.lineWidth && (t && (a.data.lineWidth = e.lineWidth), a.material.linewidth = e.lineWidth), e?.mainTagColor && (t && (a.data.mainTagColor = e.mainTagColor), document.querySelector(`.${a.data.name}-main-tag`).style.color = e.mainTagColor), e?.mainTagBackground && (t && (a.data.mainTagBackground = e.mainTagBackground), document.querySelector(`.${a.data.name}-main-tag`).style.background = e.mainTagBackground), e?.mainTagOpacity && (t && (a.data.mainTagOpacity = e.mainTagOpacity), document.querySelector(`.${a.data.name}-main-tag`).style.opacity = e.mainTagOpacity.toString()), e?.mainTagVisible !== void 0 && (t && (a.data.mainTagVisible = e.mainTagVisible), a.css3dMainTag.visible = e.mainTagVisible), e?.distanceTagColor && (t && (a.data.distanceTagColor = e.distanceTagColor), document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach((r) => r.style.color = e.distanceTagColor)), e?.distanceTagBackground && (t && (a.data.distanceTagBackground = e.distanceTagBackground), document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach((r) => r.style.background = e.distanceTagBackground)), e?.distanceTagOpacity && (t && (a.data.distanceTagOpacity = e.distanceTagOpacity), document.querySelectorAll(`.${a.data.name}-distance-tag`)?.forEach((r) => r.style.opacity = e.distanceTagOpacity.toString())), e?.distanceTagVisible !== void 0 && (t && (a.data.distanceTagVisible = e.distanceTagVisible), a.css3dTags.forEach((o) => o.visible = e.distanceTagVisible)), e?.title !== void 0 && (t && (a.data.title = e.title), this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText = e.title), e?.note !== void 0 && t && (a.data.note = e.note), a.events.fire(j);
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this, s = [];
    for (let o = 0, r = t.data.points.length / 3; o < r; o++)
      s.push(new k(t.data.points[o * 3], t.data.points[o * 3 + 1], t.data.points[o * 3 + 2]));
    let i, a;
    for (let o = 1; o < s.length; o++)
      i = s[o - 1], a = s[o], t.css3dTags[o - 1].element.childNodes[0].innerText = (i.distanceTo(a) * e).toFixed(2) + " m";
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return;
    const t = this, s = t.data.points.length, i = new k().fromArray(t.data.points.slice(s - 6, s - 3)), a = new k().fromArray(t.data.points.slice(s - 3));
    if ((!e || i.distanceTo(a) < 1e-3) && (t.data.points.pop(), t.data.points.pop(), t.data.points.pop(), t.draw(t.data)), t.events.fire(lt), t.data.points.length < 6) {
      t.dispose();
      return;
    } else {
      for (; t.css3dTags.length > t.data.points.length / 3 - 1; ) {
        const l = t.css3dTags.pop();
        t.group.remove(l), l.element.parentElement?.removeChild(l.element);
      }
      t.css3dTags[t.css3dTags.length - 1].visible = !0;
    }
    const o = parent?.onActiveMark, r = t.getMarkData(!0);
    r.isNew = !0, r.meterScale = t.events.fire(F).meterScale, o?.(r);
  }
  /**
   * 根据数据直接绘制
   */
  draw(e, t = !1) {
    if (this.disposed) return;
    const s = this;
    this.css3dTags = this.css3dTags || [];
    const i = {
      type: "MarkMultiLines",
      name: e.name || "lines" + Date.now(),
      points: [...e.points],
      lineColor: e.lineColor || "#eeee00",
      lineWidth: e.lineWidth || 3,
      mainTagColor: e.mainTagColor || "#c4c4c4",
      mainTagBackground: e.mainTagBackground || "#2E2E30",
      mainTagOpacity: e.mainTagOpacity || 0.8,
      mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
      distanceTagColor: e.distanceTagColor || "#000000",
      distanceTagBackground: e.distanceTagBackground || "#e0ffff",
      distanceTagOpacity: e.distanceTagOpacity || 0.9,
      distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
      title: e.title || "标记线" + (document.querySelectorAll(".mark-wrap-lines.main-warp").length + 1),
      note: e.note || ""
    }, a = s.geometry, o = s.material, r = new sn();
    r.setPositions([...i.points]);
    const l = new Pt({ color: i.lineColor, linewidth: i.lineWidth });
    l.resolution.set(innerWidth, innerHeight), s.copy(new yt(r, l)), a?.dispose(), o?.dispose();
    const A = i.name, c = i.points.length / 3 - 1;
    if (!s.css3dMainTag) {
      const d = document.createElement("div");
      d.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${A}-main-tag" style="color:${i.mainTagColor};background:${i.mainTagBackground};opacity:${i.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${i.title}</span>
                                     </div>`, d.classList.add("mark-wrap-lines", `${i.name}`, "main-warp"), d.style.position = "absolute", d.style.borderRadius = "4px", d.style.cursor = "pointer", d.onclick = () => {
        if (s.events.fire(F).markMode) return;
        const p = parent?.onActiveMark, m = s.getMarkData(!0);
        m.meterScale = s.events.fire(F).meterScale, p?.(m), s.events.fire(me);
      }, d.oncontextmenu = (p) => p.preventDefault();
      const h = new we(d);
      h.position.set(i.points[0], i.points[1], i.points[2]), h.element.style.pointerEvents = "none", h.scale.set(0.01, 0.01, 0.01), h.visible = i.mainTagVisible, s.group.add(h), s.css3dMainTag = h;
    }
    for (let d = s.css3dTags.length; d < c; d++) {
      const h = new k().fromArray(i.points.slice(d * 3, d * 3 + 3)), p = new k().fromArray(i.points.slice(d * 3 + 3, d * 3 + 6)), m = new k((h.x + p.x) / 2, (h.y + p.y) / 2, (h.z + p.z) / 2), g = (h.distanceTo(p) * s.events.fire(F).meterScale).toFixed(2) + " m", f = document.createElement("div");
      f.innerHTML = `<span class="${A}-distance-tag ${A}-distance-tag${d}" style="color:${i.distanceTagColor};background:${i.distanceTagBackground};opacity:${i.distanceTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${g}</span>`, f.classList.add("mark-wrap-lines", `${A}`, "distance-warp"), f.style.position = "absolute", f.style.borderRadius = "4px", f.style.display = "none";
      const u = new we(f);
      u.position.copy(m), u.element.style.pointerEvents = "none", u.scale.set(8e-3, 8e-3, 8e-3), u.visible = i.distanceTagVisible, s.css3dTags.push(u), s.group.add(u);
    }
    t || (s.css3dTags[s.css3dTags.length - 1].visible = !1), s.data = i, s.events.fire(ct, s);
  }
  getMarkData(e = !1) {
    const t = { ...this.data };
    return e ? delete t.points : t.points = [...t.points], t;
  }
  dispose() {
    if (this.disposed) return;
    const e = this;
    e.disposed = !0, e.events.fire(tt, e), e.events.fire(V).remove(e), e.events.fire(Ut, e), e.geometry.dispose(), e.material.dispose(), document.querySelectorAll(`.${e.data.name}`).forEach((s) => s.parentElement?.removeChild(s)), e.events = null, e.data = null, e.css3dTags = null, e.group = null;
  }
}
class Er extends on {
  constructor(e, t, s) {
    super(), this.isMark = !0, this.disposed = !1, this.events = e;
    const i = this;
    let a;
    if (t instanceof k) {
      const l = document.querySelectorAll(".mark-wrap-point").length + 1;
      a = {
        type: "MarkSinglePoint",
        name: s || "point" + Date.now(),
        point: t.toArray(),
        iconName: "#svgicon-point2",
        iconColor: "#eeee00",
        iconOpacity: 0.8,
        mainTagColor: "#c4c4c4",
        mainTagBackground: "#2E2E30",
        mainTagOpacity: 0.8,
        title: "标记点" + l,
        note: ""
      };
    } else
      a = {
        type: "MarkSinglePoint",
        name: t.name || "point" + Date.now(),
        point: [...t.point],
        iconName: t.iconName || "#svgicon-point2",
        iconColor: t.iconColor || "#eeee00",
        iconOpacity: t.iconOpacity || 0.8,
        mainTagColor: t.mainTagColor || "#c4c4c4",
        mainTagBackground: t.mainTagBackground || "#2E2E30",
        mainTagOpacity: t.mainTagOpacity || 0.8,
        title: t.title || "标记点",
        note: t.note || ""
      };
    const o = document.createElement("div");
    o.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${a.name}" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                <svg height="20" width="20" style="color:${a.iconColor};opacity:${a.iconOpacity};"><use href="${a.iconName}" fill="currentColor" /></svg>
                             </div>`, o.classList.add("mark-wrap-point", `mark-wrap-${a.name}`), o.style.position = "absolute", o.style.borderRadius = "4px", o.style.cursor = "pointer", o.onclick = () => {
      if (i.events.fire(F).markMode) return;
      const l = parent?.onActiveMark;
      l?.(i.getMarkData(!0)), i.events.fire(me);
    }, o.oncontextmenu = (l) => l.preventDefault();
    const r = new we(o);
    r.position.set(a.point[0], a.point[1], a.point[2]), r.element.style.pointerEvents = "none", r.scale.set(0.01, 0.01, 0.01), i.data = a, i.css3dTag = r, i.add(r), e.fire(ct, i);
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0) {
    if (this.disposed) return;
    const s = this;
    if (e?.iconName) {
      t && (s.data.iconName = e.iconName);
      const i = this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);
      i.innerHTML = `<use href="${e.iconName}" fill="currentColor" />`;
    }
    if (e?.iconColor) {
      t && (s.data.iconColor = e.iconColor);
      const i = this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);
      i.style.color = e.iconColor;
    }
    if (e?.iconOpacity) {
      t && (s.data.iconOpacity = e.iconOpacity);
      const i = this.css3dTag.element.querySelector(`.mark-wrap-${s.data.name} svg`);
      i.style.opacity = e.iconOpacity.toString();
    }
    e?.mainTagColor && (t && (s.data.mainTagColor = e.mainTagColor), this.css3dTag.element.querySelector(`.${s.data.name}`).style.color = e.mainTagColor), e?.mainTagBackground && (t && (s.data.mainTagBackground = e.mainTagBackground), this.css3dTag.element.querySelector(`.${s.data.name}`).style.background = e.mainTagBackground), e?.mainTagOpacity && (t && (s.data.mainTagOpacity = e.mainTagOpacity), this.css3dTag.element.querySelector(`.${s.data.name}`).style.opacity = e.mainTagOpacity.toString()), e?.title !== void 0 && (t && (s.data.title = e.title), this.css3dTag.element.querySelector(`.${s.data.name}`).innerText = e.title), e?.note !== void 0 && t && (s.data.note = e.note), s.events.fire(j);
  }
  resetMeterScale(e) {
    e?.meterScale !== void 0 && (this.events.fire(F).meterScale = e.meterScale);
  }
  /**
   * 绘制结束
   */
  drawFinish() {
    if (this.disposed) return;
    const e = this;
    e.events.fire(lt);
    const t = parent?.onActiveMark, s = e.getMarkData(!0);
    s.isNew = !0, s.meterScale = e.events.fire(F).meterScale, t?.(s);
  }
  getMarkData(e = !1) {
    const t = { ...this.data };
    return e ? delete t.point : t.point = [...t.point], t;
  }
  dispose() {
    if (this.disposed) return;
    const e = this;
    e.disposed = !0, e.events.fire(tt, e), e.events.fire(V).remove(e), e.events.fire(Ut, e);
    const t = document.querySelector(`.mark-wrap-${e.data.name}`);
    t?.parentElement?.removeChild?.(t), e.events = null, e.data = null, e.css3dTag = null;
  }
}
class pi extends yt {
  constructor(e) {
    super(), this.isMark = !0, this.disposed = !1, this.css3dTags = [], this.group = new on(), this.add(this.group), this.events = e;
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return;
    const s = document.querySelectorAll(".mark-wrap-plans.main-warp").length + 1, i = {
      type: "MarkMultiPlans",
      name: t || "plans" + Date.now(),
      points: [...e.toArray(), ...e.toArray()],
      lineColor: "#eeee00",
      lineWidth: 3,
      mainTagColor: "#c4c4c4",
      mainTagBackground: "#2E2E30",
      mainTagOpacity: 0.8,
      mainTagVisible: !0,
      areaTagColor: "#000000",
      areaTagBackground: "#e0ffff",
      areaTagOpacity: 0.8,
      distanceTagVisible: !0,
      areaTagVisible: !0,
      planOpacity: 0.5,
      title: "标记面" + s,
      note: ""
    };
    this.draw(i);
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, s, i = !1) {
    if (this.disposed) return;
    const a = this;
    if (s)
      if (i) {
        const o = a.data.points.length, r = a.css3dTags.length - 1, l = new k().fromArray(a.data.points.slice(o - 6, o - 3)), A = s, c = new k((l.x + A.x) / 2, (l.y + A.y) / 2, (l.z + A.z) / 2), h = (l.distanceTo(A) * a.events.fire(F).meterScale).toFixed(2) + " m";
        a.css3dTags[r].element.innerText = h, a.css3dTags[r].position.set(c.x, c.y, c.z), a.css3dTags[r].visible = !0, a.data.points.pop(), a.data.points.pop(), a.data.points.pop(), a.data.points = [...a.data.points, ...s.toArray(), ...s.toArray()], this.draw(this.data);
      } else {
        const o = a.data.points.length;
        a.data.points[o - 3] = s.x, a.data.points[o - 2] = s.y, a.data.points[o - 1] = s.z;
        const r = a.css3dTags.length - 1, l = new k().fromArray(a.data.points.slice(o - 6, o - 3)), A = new k().fromArray(a.data.points.slice(o - 3)), c = new k((l.x + A.x) / 2, (l.y + A.y) / 2, (l.z + A.z) / 2), d = l.distanceTo(A), h = (d * a.events.fire(F).meterScale).toFixed(2) + " m";
        a.css3dTags[r].element.innerText = h, a.geometry.setPositions([...a.data.points]), a.css3dTags[r].position.set(c.x, c.y, c.z), a.css3dTags[r].visible = i ? !0 : d > 0.5, a.meshPlans.geometry.setAttribute("position", new xt(new Float32Array(a.data.points), 3)), a.meshPlans.geometry.attributes.position.needsUpdate = !0;
        const p = a.events.fire(Xs, a.data.points);
        a.css3dAreaTag.position.copy(p);
        const m = a.events.fire(Js, a.data.points);
        a.css3dAreaTag.element.childNodes[0].innerText = m.toFixed(2) + " m²";
      }
    if (e?.lineColor && (t && (a.data.lineColor = e.lineColor), a.material.color.set(e.lineColor), a.meshPlans.material.color.set(e.lineColor)), e?.lineWidth && (t && (a.data.lineWidth = e.lineWidth), a.material.linewidth = e.lineWidth), e?.mainTagColor && (t && (a.data.mainTagColor = e.mainTagColor), document.querySelector(`.${a.data.name}-main-tag`).style.color = e.mainTagColor), e?.mainTagBackground && (t && (a.data.mainTagBackground = e.mainTagBackground), document.querySelector(`.${a.data.name}-main-tag`).style.background = e.mainTagBackground), e?.mainTagOpacity && (t && (a.data.mainTagOpacity = e.mainTagOpacity), document.querySelector(`.${a.data.name}-main-tag`).style.opacity = e.mainTagOpacity.toString()), e?.mainTagVisible !== void 0 && (t && (a.data.mainTagVisible = e.mainTagVisible), a.css3dMainTag.visible = e.mainTagVisible), e?.areaTagVisible !== void 0 && (t && (a.data.areaTagVisible = e.areaTagVisible), a.css3dAreaTag.visible = e.areaTagVisible), e?.areaTagColor) {
      t && (a.data.areaTagColor = e.areaTagColor);
      const o = document.querySelector(`.${a.data.name}-area-tag`);
      o && (o.style.color = e.areaTagColor);
    }
    if (e?.areaTagBackground) {
      t && (a.data.areaTagBackground = e.areaTagBackground);
      const o = document.querySelector(`.${a.data.name}-area-tag`);
      o && (o.style.background = e.areaTagBackground);
    }
    if (e?.areaTagOpacity) {
      t && (a.data.areaTagOpacity = e.areaTagOpacity);
      const o = document.querySelector(`.${a.data.name}-area-tag`);
      o && (o.style.opacity = e.areaTagOpacity.toString());
    }
    e?.distanceTagVisible !== void 0 && (t && (a.data.distanceTagVisible = e.distanceTagVisible), a.css3dTags.forEach((o) => o.visible = e.distanceTagVisible)), e?.planOpacity && (t && (a.data.planOpacity = e.planOpacity), this.meshPlans.material.opacity = e.planOpacity), e?.title !== void 0 && (t && (a.data.title = e.title), this.css3dMainTag.element.querySelector(`.${a.data.name}-main-tag`).innerText = e.title), e?.note !== void 0 && t && (a.data.note = e.note), a.events.fire(j);
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this, s = [];
    for (let r = 0, l = t.data.points.length / 3; r < l; r++)
      s.push(new k(t.data.points[r * 3], t.data.points[r * 3 + 1], t.data.points[r * 3 + 2]));
    let i, a;
    for (let r = 1; r < s.length; r++)
      i = s[r - 1], a = s[r], t.css3dTags[r - 1].element.innerText = (i.distanceTo(a) * e).toFixed(2) + " m";
    const o = t.events.fire(fa, s, e);
    t.css3dAreaTag.element.childNodes[0].innerText = o.toFixed(2) + " m²";
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return;
    const t = this, s = t.data.points.length, i = new k().fromArray(t.data.points.slice(s - 6, s - 3)), a = new k().fromArray(t.data.points.slice(s - 3));
    if ((!e || i.distanceTo(a) < 1e-4) && (t.data.points.pop(), t.data.points.pop(), t.data.points.pop()), t.events.fire(lt), t.data.points.length < 9) {
      t.dispose();
      return;
    }
    for (; t.css3dTags.length > t.data.points.length / 3 - 1; ) {
      const l = t.css3dTags.pop();
      t.group.remove(l), l.element.parentElement?.removeChild(l.element);
    }
    t.data.points.push(t.data.points[0], t.data.points[1], t.data.points[2]), t.draw(t.data, !0), t.css3dTags[t.css3dTags.length - 1].visible = !0;
    const o = parent?.onActiveMark, r = t.getMarkData(!0);
    r.isNew = !0, r.meterScale = t.events.fire(F).meterScale, o?.(r);
  }
  /**
   * 根据数据直接绘制
   */
  draw(e, t = !1) {
    if (this.disposed) return;
    const s = this;
    this.css3dTags = this.css3dTags || [];
    const i = {
      type: "MarkMultiPlans",
      name: e.name || "plans" + Date.now(),
      points: [...e.points],
      lineColor: e.lineColor || "#eeee00",
      lineWidth: e.lineWidth || 3,
      mainTagColor: e.mainTagColor || "#c4c4c4",
      mainTagBackground: e.mainTagBackground || "#2E2E30",
      mainTagOpacity: e.mainTagOpacity || 0.8,
      mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
      areaTagColor: e.areaTagColor || "#000000",
      areaTagBackground: e.areaTagBackground || "#e0ffff",
      areaTagOpacity: e.areaTagOpacity || 0.9,
      areaTagVisible: e.areaTagVisible === void 0 ? !0 : e.areaTagVisible,
      distanceTagVisible: e.distanceTagVisible === void 0 ? !0 : e.distanceTagVisible,
      planOpacity: e.planOpacity || 0.5,
      title: e.title || "标记面" + (document.querySelectorAll(".mark-wrap-plans.main-warp").length + 1),
      note: e.note || ""
    }, a = s.geometry, o = s.material, r = new sn();
    r.setPositions([...i.points]);
    const l = new Pt({ color: i.lineColor, linewidth: i.lineWidth });
    l.resolution.set(innerWidth, innerHeight), s.copy(new yt(r, l)), a?.dispose(), o?.dispose();
    const A = i.name, c = i.points.length / 3 - 1;
    if (!s.css3dMainTag) {
      const p = document.createElement("div");
      p.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                        <span class="${A}-main-tag" style="color:${i.mainTagColor};background:${i.mainTagBackground};opacity:${i.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${i.title}</span>
                                     </div>`, p.classList.add("mark-wrap-plans", `${i.name}`, "main-warp"), p.style.position = "absolute", p.style.borderRadius = "4px", p.style.cursor = "pointer", p.onclick = () => {
        if (s.events.fire(F).markMode) return;
        const y = parent?.onActiveMark, g = s.getMarkData(!0);
        g.meterScale = s.events.fire(F).meterScale, y?.(g), s.events.fire(me);
      }, p.oncontextmenu = (y) => y.preventDefault();
      const m = new we(p);
      m.position.set(i.points[0], i.points[1], i.points[2]), m.element.style.pointerEvents = "none", m.scale.set(0.01, 0.01, 0.01), m.visible = i.mainTagVisible, s.group.add(m), s.css3dMainTag = m;
    }
    const d = s.events.fire(Js, i.points).toFixed(2) + " m²", h = s.events.fire(Xs, i.points);
    if (s.css3dAreaTag)
      s.css3dAreaTag.position.copy(h), s.css3dAreaTag.element.childNodes[0].innerText = d;
    else {
      const p = document.createElement("div");
      p.innerHTML = `<span class="${A}-area-tag" style="color:${i.areaTagColor};background:${i.areaTagBackground};opacity:${i.areaTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: none;">${d}</span>`, p.classList.add("mark-wrap-plans", `${i.name}`, "area-warp"), p.style.position = "absolute", p.style.borderRadius = "4px";
      const m = new we(p);
      m.position.copy(h), m.element.style.pointerEvents = "none", m.scale.set(0.01, 0.01, 0.01), s.group.add(m), s.css3dAreaTag = m;
    }
    s.css3dAreaTag.visible = i.points.length > 6 && i.areaTagVisible;
    for (let p = s.css3dTags.length; p < c; p++) {
      const m = new k().fromArray(i.points.slice(p * 3, p * 3 + 3)), y = new k().fromArray(i.points.slice(p * 3 + 3, p * 3 + 6)), g = new k((m.x + y.x) / 2, (m.y + y.y) / 2, (m.z + y.z) / 2), u = (m.distanceTo(y) * s.events.fire(F).meterScale).toFixed(2) + " m", v = document.createElement("div");
      v.innerText = u, v.style.color = "white", v.classList.add("mark-wrap-plans", `${A}`, "distance-warp"), v.style.position = "absolute", v.style.borderRadius = "4px", v.style.display = "none";
      const x = new we(v);
      x.position.copy(g), x.element.style.pointerEvents = "none", x.scale.set(8e-3, 8e-3, 8e-3), x.visible = i.distanceTagVisible, s.css3dTags.push(x), s.group.add(x);
    }
    s.drawPlans(i), t || (s.css3dTags[s.css3dTags.length - 1].visible = !1), s.data = i, s.events.fire(ct, s);
  }
  drawPlans(e) {
    const t = this;
    if (!t.meshPlans) {
      const s = new Yn();
      s.setAttribute("position", new xt(new Float32Array(), 3)), s.attributes.position.needsUpdate = !0;
      const i = new xt(new Uint16Array([]), 1);
      s.setIndex(i);
      const a = new at({
        color: e.lineColor,
        transparent: !0,
        opacity: e.planOpacity,
        side: et
      });
      t.meshPlans = new Ie(s, a), t.meshPlans.renderOrder = 1, t.meshPlans.isMark = !0, t.group.add(t.meshPlans);
    }
    if (e.points.length > 6) {
      t.meshPlans.geometry.setAttribute("position", new xt(new Float32Array(e.points), 3)), t.meshPlans.geometry.attributes.position.needsUpdate = !0;
      let s = e.points.length / 3 - 2;
      const i = new k().fromArray(e.points.slice(0, 3)), a = new k().fromArray(e.points.slice(-3));
      i.distanceTo(a) < 1e-4 && s--;
      const o = new Uint16Array(s * 3);
      for (let r = 0; r < s; r++)
        o[r * 3] = 0, o[r * 3 + 1] = r + 1, o[r * 3 + 2] = r + 2;
      t.meshPlans.geometry.setIndex(new xt(o, 1));
    }
  }
  getMarkData(e = !1) {
    const t = { ...this.data };
    return e ? delete t.points : t.points = [...t.points], t;
  }
  dispose() {
    if (this.disposed) return;
    const e = this;
    e.disposed = !0, e.events.fire(tt, e), e.events.fire(V).remove(e), e.events.fire(Ut, e), e.geometry.dispose(), e.material.dispose(), document.querySelectorAll(`.${e.data.name}`).forEach((s) => s.parentElement?.removeChild(s)), e.events = null, e.data = null, e.css3dTags = null, e.group = null, e.meshPlans = null, e.css3dMainTag = null, e.css3dAreaTag = null;
  }
}
class br extends on {
  constructor(e) {
    super(), this.isMark = !0, this.disposed = !1, this.events = e;
  }
  /**
   * 绘制开始
   */
  drawStart(e, t) {
    if (this.disposed) return;
    const s = this, i = document.querySelectorAll(".mark-wrap-circle.main-warp").length + 1, a = {
      type: "MarkCirclePlan",
      name: t || "circle" + Date.now(),
      startPoint: e.toArray(),
      radius: 0.05,
      circleColor: "#eeee00",
      circleOpacity: 0.5,
      mainTagColor: "#c4c4c4",
      mainTagBackground: "#2E2E30",
      mainTagOpacity: 0.8,
      mainTagVisible: !0,
      circleTagColor: "#000000",
      circleTagBackground: "#e0ffff",
      circleTagOpacity: 0.9,
      circleTagVisible: !0,
      title: "标记圆面" + i
    }, o = new gt(a.radius, 32), r = new at({ color: a.circleColor, side: et, transparent: !0 });
    r.opacity = a.circleOpacity;
    const l = new Ie(o, r);
    l.position.copy(e), l.isMark = !0, l.renderOrder = 1;
    const A = new k(0, 1, 0).normalize();
    l.lookAt(l.position.clone().add(A));
    const c = document.createElement("div");
    c.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                    <span class="${a.name}-main-tag" style="color:${a.mainTagColor};background:${a.mainTagBackground};opacity:${a.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;user-select: none;font-size: 12px;pointer-events: auto;">${a.title}</span>
                                 </div>`, c.classList.add("mark-wrap-circle", `${a.name}`, "main-warp"), c.style.position = "absolute", c.style.borderRadius = "4px", c.style.cursor = "pointer", c.onclick = () => {
      if (s.events.fire(F).markMode) return;
      const m = parent?.onActiveMark;
      m?.(s.getMarkData(!0)), s.events.fire(me);
    }, c.oncontextmenu = (m) => m.preventDefault();
    const d = new we(c);
    d.position.copy(e), d.element.style.pointerEvents = "none", d.scale.set(0.01, 0.01, 0.01), d.visible = a.mainTagVisible;
    const h = document.createElement("div");
    h.innerHTML = `<span class="${t}-circle-tag" style="color:${a.circleTagColor};background:${a.circleTagBackground};opacity:${a.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;"></span>`, h.classList.add("mark-wrap-circle", `${t}`, "circle-warp"), h.style.position = "absolute", h.style.borderRadius = "4px", h.style.pointerEvents = "none";
    const p = new we(h);
    p.position.set(e.x + Math.min(a.radius / 2, 0.5), e.y, e.z), p.element.style.pointerEvents = "none", p.scale.set(8e-3, 8e-3, 8e-3), p.visible = !1, s.add(l, d, p), s.data = a, s.circleMesh = l, s.css3dTag = p, s.css3dMainTag = d, s.events.fire(ct, s);
  }
  /**
   * 绘制更新
   */
  drawUpdate(e, t = !0, s) {
    if (this.disposed) return;
    const i = this;
    if (s) {
      const a = new k().fromArray(i.data.startPoint), o = a.distanceTo(new k(s.x, a.y, s.z));
      t && (i.data.radius = o), i.circleMesh.geometry.copy(new gt(o, 128)), i.css3dTag.visible = o > 0.3;
      const r = o * i.events.fire(F).meterScale, l = (Math.PI * r * r).toFixed(2) + " m²";
      i.css3dTag.element.childNodes[0].innerText = l, i.css3dTag.position.set(a.x + Math.min(i.data.radius / 2, 0.5), a.y, a.z);
    }
    if (e?.radius) {
      const a = new k().fromArray(i.data.startPoint), o = e?.radius;
      t && (i.data.radius = o), i.circleMesh.geometry.copy(new gt(o, 128)), i.css3dTag.visible = o > 0.3;
      const r = o * i.events.fire(F).meterScale, l = (Math.PI * r * r).toFixed(2) + " m²";
      i.css3dTag.element.childNodes[0].innerText = l, i.css3dTag.position.set(a.x + Math.min(o / 2, 0.5), a.y, a.z);
    }
    e?.circleColor && (t && (i.data.circleColor = e.circleColor), i.circleMesh.material.color.set(e.circleColor)), e?.circleOpacity && (t && (i.data.circleOpacity = e.circleOpacity), i.circleMesh.material.opacity = e.circleOpacity), e?.mainTagColor && (t && (i.data.mainTagColor = e.mainTagColor), document.querySelector(`.${i.data.name}-main-tag`).style.color = e.mainTagColor), e?.mainTagBackground && (t && (i.data.mainTagBackground = e.mainTagBackground), document.querySelector(`.${i.data.name}-main-tag`).style.background = e.mainTagBackground), e?.mainTagOpacity && (t && (i.data.mainTagOpacity = e.mainTagOpacity), document.querySelector(`.${i.data.name}-main-tag`).style.opacity = e.mainTagOpacity.toString()), e?.mainTagVisible !== void 0 && (t && (i.data.mainTagVisible = e.mainTagVisible), i.css3dMainTag.visible = e.mainTagVisible), e?.circleTagColor && (t && (i.data.circleTagColor = e.circleTagColor), document.querySelector(`.${i.data.name}-circle-tag`).style.color = e.circleTagColor), e?.circleTagBackground && (t && (i.data.circleTagBackground = e.circleTagBackground), document.querySelector(`.${i.data.name}-circle-tag`).style.background = e.circleTagBackground), e?.circleTagOpacity && (t && (i.data.circleTagOpacity = e.circleTagOpacity), document.querySelector(`.${i.data.name}-circle-tag`).style.opacity = e.circleTagOpacity.toString()), e?.circleTagVisible !== void 0 && (t && (i.data.circleTagVisible = e.circleTagVisible), i.css3dTag.visible = e.circleTagVisible), e?.title !== void 0 && (t && (i.data.title = e.title), this.css3dMainTag.element.querySelector(`.${i.data.name}-main-tag`).innerText = e.title), e?.note !== void 0 && t && (i.data.note = e.note), i.events.fire(j);
  }
  /**
   * 按米标比例尺重新计算更新渲染
   */
  updateByMeterScale(e) {
    const t = this, s = t.data.radius * e, i = (Math.PI * s * s).toFixed(2) + " m²";
    t.css3dTag.element.childNodes[0].innerText = i;
  }
  /**
   * 绘制结束
   */
  drawFinish(e) {
    if (this.disposed) return;
    const t = this, s = new k().fromArray(t.data.startPoint), i = s.distanceTo(new k(e.x, s.y, e.z));
    t.data.radius = i, t.circleMesh.geometry.copy(new gt(i, 128));
    const a = t.data.radius * t.events.fire(F).meterScale, o = (Math.PI * a * a).toFixed(2) + " m²";
    t.css3dTag.element.childNodes[0].innerText = o, t.events.fire(lt);
    const r = parent?.onActiveMark, l = t.getMarkData(!0);
    l.isNew = !0, l.meterScale = t.events.fire(F).meterScale, r?.(l);
  }
  /**
   * 根据数据直接绘制
   */
  draw(e) {
    if (this.disposed) return;
    const t = this, s = {
      type: "MarkCirclePlan",
      name: e.name || "circle" + Date.now(),
      startPoint: [...e.startPoint],
      radius: e.radius,
      circleColor: e.circleColor || "#eeee00",
      circleOpacity: e.circleOpacity || 0.5,
      mainTagColor: e.mainTagColor || "#c4c4c4",
      mainTagBackground: e.mainTagBackground || "#2E2E30",
      mainTagOpacity: e.mainTagOpacity || 0.8,
      mainTagVisible: e.mainTagVisible === void 0 ? !0 : e.mainTagVisible,
      circleTagColor: e.circleTagColor || "#000000",
      circleTagBackground: e.circleTagBackground || "#e0ffff",
      circleTagOpacity: e.circleTagOpacity || 0.9,
      circleTagVisible: e.circleTagVisible === void 0 ? !0 : e.circleTagVisible,
      title: e.title || "标记圆面"
    }, i = new gt(s.radius, 128), a = new at({ color: s.circleColor, side: et, transparent: !0 });
    a.opacity = s.circleOpacity;
    const o = new Ie(i, a);
    o.position.fromArray(s.startPoint), o.isMark = !0, o.renderOrder = 1;
    const r = new k(0, 1, 0).normalize();
    o.lookAt(o.position.clone().add(r));
    const l = s.name, A = document.createElement("div");
    A.innerHTML = `<div style='flex-direction: column;align-items: center;display: flex;pointer-events: none;margin-bottom: 40px;'>
                                <span class="${l}-main-tag" style="color:${s.mainTagColor};background:${s.mainTagBackground};opacity:${s.mainTagOpacity};padding:1px 5px 2px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: auto;">${s.title}</span>
                             </div>`, A.classList.add("mark-wrap-circle", `${s.name}`, "main-warp"), A.style.position = "absolute", A.style.borderRadius = "4px", A.style.cursor = "pointer", A.onclick = () => {
      if (t.events.fire(F).markMode) return;
      const g = parent?.onActiveMark;
      g?.(t.getMarkData(!0)), t.events.fire(me);
    }, A.oncontextmenu = (g) => g.preventDefault();
    const c = new k().fromArray(s.startPoint), d = s.radius * t.events.fire(F).meterScale, h = (Math.PI * d * d).toFixed(2) + " m²", p = new we(A);
    p.position.copy(c), p.element.style.pointerEvents = "none", p.scale.set(0.01, 0.01, 0.01), p.visible = s.mainTagVisible;
    const m = document.createElement("div");
    m.innerHTML = `<span class="${l}-distance-tag ${l}-circle-tag" style="color:${s.circleTagColor};background:${s.circleTagBackground};opacity:${s.circleTagOpacity};padding: 1px 5px;border-radius: 4px;margin-bottom: 5px;user-select: none;font-size: 12px;pointer-events: none;">${h}</span>`, m.classList.add("mark-wrap-circle", `${l}`, "circle-warp"), m.style.position = "absolute", m.style.borderRadius = "4px", m.style.pointerEvents = "none";
    const y = new we(m);
    y.position.set(c.x + Math.min(s.radius / 2, 0.5), c.y, c.z), y.element.style.pointerEvents = "none", y.scale.set(8e-3, 8e-3, 8e-3), y.visible = s.circleTagVisible, t.add(o, p, y), t.data = s, t.circleMesh = o, t.css3dTag = y, t.css3dMainTag = p, t.events.fire(ct, t);
  }
  getMarkData(e = !1) {
    const t = { ...this.data };
    return e ? (delete t.startPoint, delete t.radius) : t.startPoint = [...t.startPoint], t;
  }
  dispose() {
    if (this.disposed) return;
    const e = this;
    e.disposed = !0, e.events.fire(tt, e), e.events.fire(V).remove(e), e.events.fire(Ut, e), document.querySelectorAll(`.${e.data.name}`).forEach((s) => s.parentElement?.removeChild(s)), e.events = null, e.data = null, e.circleMesh = null, e.css3dTag = null, e.css3dMainTag = null;
  }
}
const KA = (() => {
  const n = new As();
  let e = !1;
  n.on(Ec, async (s = "", i = !1, a = 0.5) => {
    const o = t();
    let r = null;
    if (new Promise((c) => r = c), o.isPlaying) {
      e ? (o?.stop(), r(!1)) : r(!0);
      return;
    }
    const l = await Ke(s || "https://reall3d.com/demo-models/demo/background1.mp3"), A = URL.createObjectURL(new Blob([l], { type: "application/octet-stream" }));
    new Or().load(
      A,
      (c) => {
        e || (o.setBuffer(c), o.setLoop(i), o.setVolume(a), o.play()), r(!e);
      },
      () => r(!1)
    );
  }), n.on(Ar, () => {
    const s = t(!1);
    s?.isPlaying && s.stop();
  }), n.on(vc, (s = !0) => ((s === !0 || s === !1) && (e = s), e)), n.on(bc, () => {
    const s = t();
    if (!s?.isPlaying) return;
    let i = s.getVolume();
    co(
      () => {
        s && (e ? s.stop() : s.setVolume(i = Math.max(i * 0.99, 0.15)));
      },
      () => i > 0.15
    );
  }), n.on(Sc, () => {
    const s = t();
    if (!s?.isPlaying) return;
    let i = s.getVolume();
    co(
      () => {
        s && (e ? s.stop() : s.setVolume(i = Math.min(i * 1.01, 0.5)));
      },
      () => i < 0.5
    );
  });
  function t(s = !0) {
    if (e) return null;
    let i = n.tryFire(oo);
    return !i && s && (i = new Xr(new Jr()), n.on(oo, () => i)), i;
  }
  return n;
})();
let ZA = class {
  constructor() {
    this.down = 0, this.move = !1, this.downTime = 0, this.isDbClick = !1, this.x = 0, this.y = 0, this.lastClickX = 0, this.lastClickY = 0, this.lastClickPointTime = 0, this.lastMovePoint = null, this.lastMovePointTime = 0, this.touchStartDistance = 0, this.touchPrevDistance = 0, this.touchStartX1 = 0, this.touchStartY1 = 0, this.touchStartX2 = 0, this.touchStartY2 = 0, this.touchPrevX1 = 0, this.touchPrevY1 = 0, this.touchPrevX2 = 0, this.touchPrevY2 = 0;
  }
  // 第二个触摸点上一帧Y坐标
};
function jA(n) {
  const e = (C, E, Q) => n.on(C, E, Q), t = (C, ...E) => n.fire(C, ...E), s = t(_t);
  let i = /* @__PURE__ */ new Set(), a, o = new ZA(), r, l = 0, A = 0;
  function c() {
    const C = t($), E = new k();
    C.getWorldDirection(E), l = Math.asin(E.y), A = Math.atan2(E.z, E.x);
  }
  e(ms, () => {
    const C = t(H), E = t(F);
    C.autoRotate = E.autoRotate = !0, C.autoRotate, E.autoRotate;
  }), e(me, (C = !0) => {
    const E = t(H), Q = t(F);
    E.autoRotate = Q.autoRotate = !1, E.autoRotate, Q.autoRotate, C && t(es);
  }), e(Is, (C = !0) => {
    if (a) return;
    const E = t(H), Q = C ? Math.PI / 128 : -(Math.PI / 128), I = new $e().makeRotationAxis(new k(0, 0, -1).transformDirection(E.object.matrixWorld), Q);
    E.object.up.transformDirection(I), t(j);
  }), e(ws, () => {
    if (a) return;
    if (t(F).useCustomControl === !0) {
      const Q = t($), I = t(V);
      let B;
      if (I.traverse((b) => {
        b instanceof Le && (B = b);
      }), B) {
        const b = B.position.clone(), T = Q.position.clone(), L = b.sub(T).normalize(), U = -(Math.PI / 128), _ = new rt().setFromAxisAngle(L, U);
        B.applyQuaternion(_);
      }
      t(j);
    } else
      n.fire(Is, !0);
  }), e(Es, () => {
    if (a) return;
    if (t(F).useCustomControl === !0) {
      const Q = t($), I = t(V);
      let B;
      if (I.traverse((b) => {
        b instanceof Le && (B = b);
      }), B) {
        const b = B.position.clone(), T = Q.position.clone(), L = b.sub(T).normalize(), U = Math.PI / 128, _ = new rt().setFromAxisAngle(L, U);
        B.applyQuaternion(_);
      }
      t(j);
    } else
      n.fire(Is, !1);
  }), e(qn, (C) => {
    const E = t(F);
    C ?? (C = !E.pointcloudMode), E.pointcloudMode = C, t(V).traverse((I) => I instanceof Le && I.fire(ft, C));
  }), e($n, () => {
    t(V).traverse((E) => E instanceof Le && E.fire(Pn));
  }), e(jt, (C) => {
    t(V).traverse((Q) => Q instanceof Le && Q.fire(jt, C));
  }), e(so, () => {
    const C = t(V);
    let E;
    C.traverse((Q) => Q instanceof Le && (E = Q)), E && t(Ks, E.fire(qa));
  }), e(mn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = I.useCustomControl === !0, b = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3, T = new k();
    if (E.getWorldDirection(T), E.position.addScaledVector(T, b), B) {
      const L = new k().copy(E.position).add(T);
      Q.target.copy(L);
    } else
      Q.target.addScaledVector(T, b);
    t(j);
  }), e(Cn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3, b = new k();
    E.getWorldDirection(b), E.position.addScaledVector(b, -B), Q.target.addScaledVector(b, -B), t(j);
  }), e(Wt, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3, b = new k(), T = new k(0, 1, 0), L = new k();
    E.getWorldDirection(L), b.crossVectors(T, L).normalize(), E.position.addScaledVector(b, -B), Q.target.addScaledVector(b, -B), t(j);
  }), e(Ht, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3, b = new k(), T = new k(0, 1, 0), L = new k();
    E.getWorldDirection(L), b.crossVectors(T, L).normalize(), E.position.addScaledVector(b, B), Q.target.addScaledVector(b, B), t(j);
  }), e(yn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = I.useCustomControl === !0, b = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3;
    if (B) {
      const T = new k(0, 1, 0).applyQuaternion(E.quaternion);
      T.toArray(), E.position.addScaledVector(T, b);
      const L = new k();
      E.getWorldDirection(L);
      const U = new k().copy(E.position).add(L);
      Q.target.copy(U);
    } else {
      const T = new k(0, 1, 0);
      E.position.addScaledVector(T, -b), Q.target.addScaledVector(T, -b);
    }
    t(j);
  }), e(In, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = t(F), B = I.useCustomControl === !0, b = C !== void 0 ? C : I.cameraMoveSpeed !== void 0 ? I.cameraMoveSpeed : 5e-3;
    if (B) {
      const T = new k(0, 1, 0).applyQuaternion(E.quaternion);
      T.toArray(), E.position.addScaledVector(T, -b);
      const L = new k();
      E.getWorldDirection(L);
      const U = new k().copy(E.position).add(L);
      Q.target.copy(U);
    } else {
      const T = new k(0, 1, 0);
      E.position.addScaledVector(T, b), Q.target.addScaledVector(T, b);
    }
    t(j);
  }), e(wn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = E.position.clone();
    l += C !== void 0 ? C : 0.05, l = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, l));
    const b = new k(Math.cos(l) * Math.cos(A), Math.sin(l), Math.cos(l) * Math.sin(A)).normalize(), T = new k().copy(I).add(b);
    Q.target.copy(T), E.lookAt(T), E.position.copy(I), t(j);
  }), e(En, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = E.position.clone();
    l -= C !== void 0 ? C : 0.05, l = Math.min(Math.PI / 2 - 0.1, Math.max(-Math.PI / 2 + 0.1, l));
    const b = new k(Math.cos(l) * Math.cos(A), Math.sin(l), Math.cos(l) * Math.sin(A)).normalize(), T = new k().copy(I).add(b);
    Q.target.copy(T), E.lookAt(T), E.position.copy(I), t(j);
  }), e(bn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = E.position.clone();
    I.toArray(), A += C !== void 0 ? C : 0.05;
    const b = new k(Math.cos(l) * Math.cos(A), Math.sin(l), Math.cos(l) * Math.sin(A)).normalize(), T = new k().copy(I).add(b);
    Q.target.copy(T), E.lookAt(T), E.position.copy(I), E.position.toArray(), Q.minDistance = Q.maxDistance = Q.getDistance(), t(j);
  }), e(Sn, (C = void 0) => {
    if (a) return;
    const E = t($), Q = t(H), I = E.position.clone();
    A -= C !== void 0 ? C : 0.05;
    const b = new k(Math.cos(l) * Math.cos(A), Math.sin(l), Math.cos(l) * Math.sin(A)).normalize(), T = new k().copy(I).add(b);
    Q.target.copy(T), E.lookAt(T), E.position.copy(I), Q.minDistance = Q.maxDistance = Q.getDistance(), t(j);
  }), e(rs, () => {
    if (!i.size) return;
    const C = t(F);
    if (!C.enableKeyboard) return i.clear();
    if (C.markMode && i.has("Escape")) {
      t(Vt), i.clear();
      return;
    }
    const E = C.useCustomControl === !0, Q = i.has("Shift");
    Q && E ? (i.has("KeyW") && t(En, 0.01), i.has("KeyS") && t(wn, 0.01), i.has("KeyA") && t(bn, 0.01), i.has("KeyD") && t(Sn, 0.01)) : E && !Q && (i.has("KeyW") && t(mn), i.has("KeyS") && t(Cn), i.has("KeyA") && t(Wt), i.has("KeyD") && t(Ht), i.has("ArrowLeft") && t(Wt), i.has("ArrowRight") && t(Ht), i.has("ArrowUp") && t(yn), i.has("ArrowDown") && t(In)), i.has("Space") ? (C.bigSceneMode ? t(qn) : t($n), i.clear()) : i.has("Escape") ? (KA.fire(Ar), i.clear()) : i.has("KeyR") ? (C.autoRotate ? t(me) : t(ms), i.clear()) : i.has("KeyM") ? (t($t, !C.markVisible), i.clear()) : E && i.has("KeyQ") ? (t(ws), i.clear()) : E && i.has("KeyE") ? (t(Es), i.clear()) : !E && i.has("ArrowLeft") ? (t(ws), t(_n, !0), i.clear()) : !E && i.has("ArrowRight") ? (t(Es), t(_n, !0), i.clear()) : i.has("KeyP") ? (t(ot, !0), i.clear()) : i.has("Equal") ? (t(rn), i.clear()) : i.has("Minus") ? (t(ln), i.clear()) : i.has("KeyY") ? (t(cn, !1), t(ti), i.clear()) : i.has("KeyI") ? (t(Xa), i.clear()) : i.has("KeyF") ? (t(so), i.clear()) : i.has("F2") ? (!C.bigSceneMode && window.open("/editor/index.html?url=" + encodeURIComponent(t(ve).meta.url)), i.clear()) : i.has("KeyW") ? (ed(t(H), 0.15), i.clear()) : i.has("KeyS") ? (td(t(H), 0.15), i.clear()) : i.has("KeyA") ? (qA(t(H), 0.15), i.clear()) : i.has("KeyD") ? ($A(t(H), 0.15), i.clear()) : i.has("KeyQ") ? (nd(t(H)), i.clear()) : i.has("KeyE") ? (sd(t(H)), i.clear()) : i.has("KeyC") ? (id(t(H)), i.clear()) : i.has("KeyZ") && (od(t(H)), i.clear());
  }), e(Kn, async (C, E) => {
    if (o.move || t(F).useCustomControl === !0)
      return;
    const B = await t(Jn, C, E);
    B.length && (B[0].toArray(), t(Ks, B[0], !0, !1));
  }), e(ke, async (C, E) => {
    const Q = t(V), I = [];
    Q.traverse((b) => b instanceof Le && I.push(b));
    const B = await t(Jn, C, E);
    return B.length ? (I.length && I[0].fire(Fn, B[0].x, B[0].y, B[0].z, !0), new k(B[0].x, B[0].y, B[0].z)) : (I.length && I[0].fire(Fn, 0, 0, 0, !1), null);
  }), e(vi, async () => {
    const C = t(V), E = [];
    C.traverse((Q) => Q instanceof Le && E.push(Q));
    for (let Q = 0; Q < E.length; Q++)
      E[Q].fire(Fn, 0, 0, 0, !1);
  });
  const d = (C) => {
    C.target.type !== "text" && (a || C.code === "F5" || (C.preventDefault(), C.code !== "KeyR" && (i.add(C.code), (C.code === "ShiftLeft" || C.code === "ShiftRight") && i.add("Shift"), t(me)), r = Date.now()));
  }, h = (C) => {
    C.target.type !== "text" && (a || (C.code === "KeyR" && i.add(C.code), (C.code === "ArrowLeft" || C.code === "ArrowRight") && t(ya), i.delete(C.code), (C.code === "ShiftLeft" || C.code === "ShiftRight") && i.delete("Shift"), r = Date.now()));
  };
  e(
    Ft,
    () => {
      t(Mi) && Date.now() - r > 2e3 && (t(_n, !1), t(ti));
      const C = t(F), E = t(H);
      C.autoRotate && !E.autoRotate && Date.now() - r > 3e3 && (C.useCustomControl || t(ms));
    },
    !0
  );
  const p = () => {
    i.clear();
  }, m = (C) => {
    if (parent && setTimeout(() => window.focus()), C.preventDefault(), a) return;
    if (t(me), t(F).useCustomControl === !0) {
      const I = t($), B = t(H), b = C.deltaY > 0 ? 1.1 : 0.9, T = I.position.clone().sub(B.target).normalize(), L = I.position.distanceTo(B.target), U = Math.max(0.1, Math.min(1e3, L * b)), _ = B.target.clone().add(T.multiplyScalar(U));
      I.position.copy(_), t(j), U.toFixed(2);
    }
    r = Date.now();
  }, y = (C) => {
    C.preventDefault(), !a && (t(me), r = Date.now());
  };
  let g, f, u, v;
  e(Vt, () => {
    g?.dispose(), f?.dispose(), u?.dispose(), v?.dispose(), g = null, f = null, u = null, v = null, o.lastMovePoint = null, t(lt);
  });
  const x = async (C) => {
    if (parent && setTimeout(() => window.focus()), C.preventDefault(), a) return;
    t(me), C.button === 1 ? o.down = 3 : o.down = C.button === 2 ? 2 : 1, o.move = !1, o.isDbClick = Date.now() - o.downTime < 300, o.x = C.clientX, o.y = C.clientY;
    const E = t(F);
    if (E.useCustomControl === !0) {
      const I = t($), B = I.position.clone();
      B.toArray();
      const b = t(H);
      b.enabled = !1, b.minDistance = b.maxDistance = b.getDistance(), b.updateControlMode(E), o.down, o.down === 1 && (C.clientX, C.clientY, Math.abs(l) < 1e-3 && Math.abs(A) < 1e-3 && c(), I.position.copy(B), C.stopPropagation());
    }
    r = Date.now(), o.downTime = Date.now();
  }, R = async (C) => {
    if (C.preventDefault(), a) return;
    const E = t(F), Q = E.useCustomControl === !0;
    if (o.down) {
      const I = C.clientX - o.x, B = C.clientY - o.y;
      if (o.down === 3) {
        C.stopPropagation();
        const b = t(H);
        b.enabled = !1;
        const T = E.cameraMoveSpeed !== void 0 ? E.cameraMoveSpeed : 5e-3, L = 0.5;
        Math.abs(B) > 2 && (B > 0 ? t(Cn, Math.abs(B) * L * T) : t(mn, Math.abs(B) * L * T)), o.x = C.clientX, o.y = C.clientY, t(j), o.move = !0, r = Date.now();
        return;
      }
      if (o.down === 2 && Q) {
        C.stopPropagation();
        const b = t(H);
        b.enabled = !1;
        const T = E.cameraMoveSpeed !== void 0 ? E.cameraMoveSpeed : 5e-3, L = 0.5;
        Math.abs(I) > 2 && (I > 0 ? t(Wt, Math.abs(I) * L * T) : t(Ht, Math.abs(I) * L * T)), Math.abs(B) > 2 && (B > 0 ? t(yn, Math.abs(B) * L * T) : t(In, Math.abs(B) * L * T)), o.x = C.clientX, o.y = C.clientY, t(j), o.move = !0, r = Date.now();
        return;
      }
      if (Q && o.down === 1) {
        C.stopPropagation();
        const b = t(H);
        b.enabled = !1;
        const T = t($), L = T.position.clone(), U = 1e-3, _ = 1e-3;
        Math.abs(l) < 1e-3 && Math.abs(A) < 1e-3 && c(), Math.abs(I) > 0 && (I > 0 ? t(Sn, Math.abs(I) * U) : t(bn, Math.abs(I) * U)), Math.abs(B) > 0 && (B > 0 ? t(wn, Math.abs(B) * _) : t(En, Math.abs(B) * _)), T.position.copy(L), b.minDistance = b.maxDistance = b.getDistance(), o.x = C.clientX, o.y = C.clientY, t(j), o.move = !0, r = Date.now();
        return;
      }
      o.move = !0, r = Date.now();
    }
    if (!Q && E.markMode) {
      const I = await t(ke, C.clientX, C.clientY);
      I && !o.down && E.markType === "distance" && g ? g.drawUpdate({ endPoint: I.toArray() }) : !o.down && E.markType === "circle" && v ? I ? (v.drawUpdate(null, !0, I), o.lastMovePoint = I, o.lastMovePointTime = Date.now()) : (o.lastMovePoint = null, o.lastMovePointTime = 0) : !o.down && E.markType === "lines" && f ? I ? (f.drawUpdate(null, !0, I), o.lastMovePoint = I, o.lastMovePointTime = Date.now()) : (o.lastMovePoint = null, o.lastMovePointTime = 0) : !o.down && E.markType === "plans" && u && (I ? (u.drawUpdate(null, !0, I), o.lastMovePoint = I, o.lastMovePointTime = Date.now()) : (o.lastMovePoint = null, o.lastMovePointTime = 0));
    }
  }, M = async (C) => {
    if (C.preventDefault(), a) return;
    const E = t(F), Q = E.useCustomControl === !0;
    if (Q) {
      const I = t($), B = t(H), b = I.position.clone();
      o.down, o.move, B.enabled = !1, B.updateControlMode(E), B.minDistance = B.maxDistance = B.getDistance(), I.position.copy(b), I.position.toArray(), C.stopPropagation();
    } else if (Q && o.down === 2 && o.move) {
      const I = t(H);
      I.enabled = !0, I.update(), C.stopPropagation();
    } else if (o.down === 3 && o.move) {
      const I = t(H);
      I.enabled = !0, I.update(), C.stopPropagation();
    }
    if (o.isDbClick && (f ? Math.abs(C.clientX - o.lastClickX) < 2 && Math.abs(C.clientY - o.lastClickY) < 2 && (f.drawFinish(o.lastClickPointTime > 0), f = null, o.lastMovePoint = null) : u && Math.abs(C.clientX - o.lastClickX) < 2 && Math.abs(C.clientY - o.lastClickY) < 2 && (u.drawFinish(o.lastClickPointTime > 0), u = null, o.lastMovePoint = null)), E.markMode && o.down === 1 && !o.move && Date.now() - o.downTime < 500) {
      if (E.markType === "point") {
        if (await t(ke, C.clientX, C.clientY)) {
          const B = new Er(n, await t(ke, C.clientX, C.clientY));
          t(V).add(B), B.drawFinish();
        }
      } else if (E.markType === "distance")
        if (g) {
          const I = await t(ke, C.clientX, C.clientY);
          I ? (g.drawFinish(I), g = null) : o.isDbClick && t(Vt);
        } else {
          const I = await t(ke, C.clientX, C.clientY);
          I && (g = new gi(n), g.drawStart(I), t(V).add(g));
        }
      else if (E.markType === "lines")
        if (f)
          if (o.lastMovePoint && t($s, C.clientX, C.clientY, o.lastMovePoint) < 0.03)
            f.drawUpdate(null, !0, o.lastMovePoint, !0), o.lastClickPointTime = Date.now();
          else {
            const I = await t(ke, C.clientX, C.clientY);
            I ? (f.drawUpdate(null, !0, I, !0), o.lastClickPointTime = Date.now()) : o.lastClickPointTime = 0;
          }
        else {
          const I = await t(ke, C.clientX, C.clientY);
          I && (f = new fi(n), f.drawStart(I), t(V).add(f));
        }
      else if (E.markType === "plans")
        if (u)
          if (o.lastMovePoint && t($s, C.clientX, C.clientY, o.lastMovePoint) < 0.03)
            u.drawUpdate(null, !0, o.lastMovePoint, !0), o.lastClickPointTime = Date.now();
          else {
            const I = await t(ke, C.clientX, C.clientY);
            I ? (u.drawUpdate(null, !0, I, !0), o.lastClickPointTime = Date.now()) : o.lastClickPointTime = 0;
          }
        else {
          const I = await t(ke, C.clientX, C.clientY);
          I && (u = new pi(n), u.drawStart(I), t(V).add(u));
        }
      else if (E.markType === "circle")
        if (v) {
          const I = await t(ke, C.clientX, C.clientY);
          I ? (v.drawFinish(I), v = null) : o.isDbClick && t(Vt);
        } else {
          const I = await t(ke, C.clientX, C.clientY);
          I && (v = new br(n), v.drawStart(I), t(V).add(v));
        }
      o.lastClickX = C.clientX, o.lastClickY = C.clientY;
    }
    o.down === 2 && !o.move && (E.useCustomControl === !0 || t(Kn, C.clientX, C.clientY)), o.down = 0, o.move = !1, r = Date.now();
  };
  function D(C) {
    if (C.preventDefault(), a) return;
    t(me), o.down = C.touches.length;
    const Q = t(F).useCustomControl === !0;
    if (o.down === 1) {
      if (o.move = !1, o.x = C.touches[0].clientX, o.y = C.touches[0].clientY, Q) {
        Math.abs(l) < 1e-3 && Math.abs(A) < 1e-3 && c();
        const I = t(H);
        I.enabled = !1;
      }
    } else if (o.down === 2 && Q) {
      const I = C.touches[0], B = C.touches[1];
      o.touchStartX1 = I.clientX, o.touchStartY1 = I.clientY, o.touchStartX2 = B.clientX, o.touchStartY2 = B.clientY, o.touchPrevX1 = I.clientX, o.touchPrevY1 = I.clientY, o.touchPrevX2 = B.clientX, o.touchPrevY2 = B.clientY;
      const b = I.clientX - B.clientX, T = I.clientY - B.clientY;
      o.touchStartDistance = Math.sqrt(b * b + T * T), o.touchPrevDistance = o.touchStartDistance, o.touchStartDistance;
      const L = t(H);
      L.enabled = !1;
    }
    r = Date.now();
  }
  function S(C) {
    if (C.preventDefault(), a) return;
    const E = t(F), Q = E.useCustomControl === !0;
    if (C.touches.length, C.touches.length === 1) {
      if (o.move = !0, Q) {
        const I = C.touches[0], B = I.clientX - o.x, b = I.clientY - o.y, T = t($), L = t(H), U = T.position.clone(), _ = 1e-3, W = 1e-3;
        Math.abs(B) > 0 && (B < 0 ? t(bn, Math.abs(B) * _) : t(Sn, Math.abs(B) * _)), Math.abs(b) > 0 && (b < 0 ? t(En, Math.abs(b) * W) : t(wn, Math.abs(b) * W)), T.position.copy(U), L.minDistance = L.maxDistance = L.getDistance(), o.x = I.clientX, o.y = I.clientY, t(j);
      }
    } else if (C.touches.length === 2 && Q) {
      console.warn("[TouchMove] 双指移动 - 在自定义控制模式下处理");
      const I = C.touches[0], B = C.touches[1], b = I.clientX - B.clientX, T = I.clientY - B.clientY, L = Math.sqrt(b * b + T * T), U = L - o.touchPrevDistance, _ = (I.clientX + B.clientX) / 2, W = (I.clientY + B.clientY) / 2, ee = (o.touchPrevX1 + o.touchPrevX2) / 2, z = (o.touchPrevY1 + o.touchPrevY2) / 2, O = _ - ee, Y = W - z, J = E.cameraMoveSpeed !== void 0 ? E.cameraMoveSpeed : 0.05, se = 0.1, le = Math.abs(U), he = Math.abs(O), Me = Math.abs(Y);
      if (le > 1) {
        const re = le * se * J;
        U > 0 ? t(mn, re) : t(Cn, re);
      }
      if (he > 1 || Me > 1) {
        if (he > Me && he > 1) {
          const re = he * se * J;
          O < 0 ? t(Ht, re) : t(Wt, re);
        } else if (Me > 1) {
          const re = Me * se * J;
          Y < 0 ? t(In, re) : t(yn, re);
        }
      }
      o.touchPrevX1 = I.clientX, o.touchPrevY1 = I.clientY, o.touchPrevX2 = B.clientX, o.touchPrevY2 = B.clientY, o.touchPrevDistance = L, t(j);
    }
    r = Date.now();
  }
  function P(C) {
    if (a) return;
    if (t(F).useCustomControl === !0) {
      const I = t(H);
      I.enabled = !1;
    }
    C.touches.length === 0 ? (o.down = 0, o.move = !1) : o.down = C.touches.length, r = Date.now();
  }
  window.addEventListener("keydown", d), window.addEventListener("keyup", h), window.addEventListener("blur", p), window.addEventListener("wheel", m, { passive: !1 }), s.addEventListener("contextmenu", y), s.addEventListener("mousedown", x), s.addEventListener("mousemove", R), s.addEventListener("mouseup", M), s.addEventListener("touchstart", D, { passive: !1 }), s.addEventListener("touchmove", S, { passive: !1 }), s.addEventListener("touchend", P, { passive: !1 }), window.addEventListener("resize", G), G();
  function G() {
    const { width: C, height: E, top: Q, left: I } = t(It), B = t($);
    B.aspect = C / E, B.updateProjectionMatrix();
    const b = t(xi);
    b.setSize(C, E), b.domElement.style.position = "absolute", b.domElement.style.left = `${I}px`, b.domElement.style.top = `${Q}px`;
    const T = t(qt);
    T.setPixelRatio(Math.min(devicePixelRatio, 2)), T.setSize(C, E);
  }
  e(wi, () => {
    a = !0, window.removeEventListener("keydown", d), window.removeEventListener("keyup", h), window.removeEventListener("blur", p), window.removeEventListener("wheel", m), s.removeEventListener("contextmenu", y), s.removeEventListener("mousedown", x), s.removeEventListener("mousemove", R), s.removeEventListener("mouseup", M), s.removeEventListener("touchstart", D), s.removeEventListener("touchmove", S), s.removeEventListener("touchend", P), window.removeEventListener("resize", G);
  });
}
function gs(n, e, t = 0.012) {
  const s = n.object.up.clone().normalize(), a = new k().copy(e).projectOnPlane(s).normalize().multiplyScalar(t);
  n.object.position.add(a), n.target.add(a), n.update();
}
function qA(n, e = 0.2) {
  const t = new k();
  n.object.getWorldDirection(t);
  const s = new k().crossVectors(n.object.up, t).normalize();
  gs(n, s, e);
}
function $A(n, e = 0.2) {
  const t = new k();
  n.object.getWorldDirection(t);
  const s = new k().crossVectors(n.object.up, t).normalize().negate();
  gs(n, s, e);
}
function ed(n, e = 0.2) {
  const t = new k();
  n.object.getWorldDirection(t), gs(n, t, e);
}
function td(n, e = 0.2) {
  const t = new k();
  n.object.getWorldDirection(t).negate(), gs(n, t, e);
}
function Sr(n, e = 6e-3) {
  const t = n.object.position.clone(), s = n.target.clone(), i = new k().subVectors(s, t), a = n.object.up.clone().normalize(), o = new rt();
  o.setFromAxisAngle(a, -e), i.applyQuaternion(o);
  const r = new k().addVectors(t, i);
  n.target.copy(r), n.update();
}
function nd(n) {
  Sr(n, -0.01);
}
function sd(n) {
  Sr(n, 0.01);
}
function vr(n, e = 6e-3) {
  const t = n.object.position.clone(), s = n.target.clone(), i = new k().subVectors(s, t), a = new k();
  n.object.getWorldDirection(a);
  const o = new k().crossVectors(n.object.up, a).normalize(), r = new rt();
  r.setFromAxisAngle(o, e), i.applyQuaternion(r);
  const l = new k().addVectors(t, i);
  n.target.copy(l), n.update();
}
function id(n, e = 0.01) {
  vr(n, -e);
}
function od(n, e = 0.01) {
  vr(n, e);
}
function Mr(n) {
  const e = new Kr(), t = 5, s = (a, o, r) => n.on(a, o, r), i = (a, ...o) => n.fire(a, ...o);
  s(Jn, async (a, o) => {
    const { width: r, height: l, left: A, top: c } = i(It), d = new ye();
    d.x = (a - A) / r * 2 - 1, d.y = (c - o) / l * 2 + 1;
    const h = (d.x + 1) / 2 * r, p = (1 - d.y) / 2 * l, m = i($);
    e.setFromCamera(d, m);
    const y = [], g = i(V), f = [], u = [];
    g.traverse(function(D) {
      D instanceof Le ? u.push(D) : D.isMesh && !D.ignoreIntersect && !D.isMark && f.push(D);
    });
    const v = e.intersectObjects(f, !0);
    for (let D = 0; D < v.length; D++)
      y.push({ point: v[D].point, d: e.ray.distanceToPoint(v[D].point), p: 1 });
    const x = i(er), R = m.projectionMatrix.clone().multiply(m.matrixWorldInverse);
    for (let D = 0; D < u.length; D++) {
      const S = u[D].fire(qs);
      if (S)
        if (S.length !== void 0) {
          const P = S, G = P.length / 3;
          for (let C = 0; C < G; C++) {
            const E = new k(P[3 * C + 0], P[3 * C + 1], P[3 * C + 2]);
            x && E.applyMatrix4(x);
            const Q = new Xe(E.x, E.y, E.z, 1).applyMatrix4(R), I = (Q.x / Q.w + 1) / 2 * r, B = (1 - Q.y / Q.w) / 2 * l, b = Math.sqrt((I - h) ** 2 + (B - p) ** 2);
            b <= t && y.push({ point: E, d: m.position.distanceTo(E), p: b });
          }
        } else
          for (let P of Object.keys(S)) {
            const G = P.split(","), C = new k(Number(G[0]), Number(G[1]), Number(G[2]));
            if (e.ray.distanceToPoint(C) <= 1.4143) {
              const E = S[P];
              for (let Q = 0, I = E.length / 3; Q < I; Q++) {
                const B = new k(E[3 * Q + 0], E[3 * Q + 1], E[3 * Q + 2]);
                x && B.applyMatrix4(x);
                const b = new Xe(B.x, B.y, B.z, 1).applyMatrix4(R), T = (b.x / b.w + 1) / 2 * r, L = (1 - b.y / b.w) / 2 * l, U = Math.sqrt((T - h) ** 2 + (L - p) ** 2);
                U <= t && y.push({ point: B, d: m.position.distanceTo(B), p: U });
              }
            }
          }
    }
    if (!y.length) return [];
    y.sort((D, S) => D.d - S.d);
    const M = [];
    for (let D = 0, S = y[0].d, P = Math.min(y.length, 20); D < P; D++)
      y[D].d - S < 0.01 && M.push(y[D]);
    return M.sort((D, S) => D.p - S.p), [M[0].point];
  }), s($s, (a, o, r) => {
    const { width: l, height: A, left: c, top: d } = i(It), h = new ye();
    h.x = (a - c) / l * 2 - 1, h.y = (d - o) / A * 2 + 1;
    const p = i($);
    return e.setFromCamera(h, p), e.ray.distanceToPoint(r);
  });
}
function ad(n) {
  const e = (h, p, m) => n.on(h, p, m), t = (h, ...p) => n.fire(h, ...p), s = t(H), i = s.target.clone();
  t(V).traverse((h) => {
    h.fire && typeof h.fire == "function" && h.fire(xa, i.x, i.y, i.z);
  }), e(an, () => t($).fov), e(Ue, (h = !1) => h ? s.object.position.clone() : s.object.position), e(Ve, (h = !1) => h ? s.target.clone() : s.target), e(Lt, (h = !1) => h ? t($).up.clone() : t($).up);
  let o;
  const r = [];
  e(Ks, (h, p = !1, m) => {
    if (t(Na, h), !p) {
      s.target.copy(h);
      const x = new k().subVectors(h, s.object.position);
      x.length() < 1 && s.object.position.copy(h).sub(x.setLength(1)), x.length() > 50 && s.object.position.copy(h).sub(x.setLength(50)), t(Zn), t(ei);
      return;
    }
    for (; r.length; ) r.pop().stop = !0;
    let y = { alpha: 0, time: Date.now(), stop: !1 };
    r.push(y), o = o || { enablePan: s.enablePan, enableRotate: s.enableRotate }, s.enablePan = !1, s.enableRotate = !1;
    const g = t(Ve, !0), f = t(Ue, !0), u = g.clone().sub(f).normalize(), v = h.clone().sub(u.multiplyScalar(h.clone().sub(f).dot(u)));
    t(
      Rt,
      () => {
        y.alpha = (Date.now() - y.time) / 600;
        const x = g.clone().lerp(h, y.alpha);
        t(H).target.copy(x), !m && t(H).object.position.copy(f.clone().lerp(v, y.alpha)), t(Zn), y.alpha >= 0.9 && (s.enablePan = o.enablePan, s.enableRotate = o.enableRotate), y.alpha >= 1 && (y.stop = !0, t(ei));
      },
      () => !y.stop
    );
  }), e(ma, () => {
    let h = t(Ue).toArray(), p = t(Lt).toArray(), m = t(Ve).toArray();
    return { position: h, lookUp: p, lookAt: m };
  }), e(Ca, () => t(H).update()), e(ya, () => t(H).updateRotateAxis());
  const l = 0.01;
  let A = new k(), c = new k(), d = 0;
  e(bi, () => {
    const h = t(H).object, p = h.fov, m = h.position.clone(), y = h.getWorldDirection(new k());
    return Math.abs(d - p) < l && Math.abs(m.x - A.x) < l && Math.abs(m.y - A.y) < l && Math.abs(m.z - A.z) < l && Math.abs(y.x - c.x) < l && Math.abs(y.y - c.y) < l && Math.abs(y.z - c.z) < l ? !1 : (d = p, A = m, c = y, !0);
  });
}
function rd(n) {
  const e = (o, r, l) => n.on(o, r, l), t = (o, ...r) => n.fire(o, ...r);
  let s = a();
  const i = [];
  e(bs, () => {
    const { height: o } = t(It), r = t(Ue).distanceTo(s.position) * 3.2 / o;
    s.scale.set(r, r, r);
  }), e(fn, (o) => {
    t(bs), s.visible = o > 0.1, s.element.style.opacity = o + "", t(j);
  }), e(Na, (o) => {
    for (s.position.copy(o); i.length; ) i.pop().stop = !0;
    t(fn, 1), t(j);
  }), e(ei, () => {
    for (; i.length; ) i.pop().stop = !0;
    let o = { opacity: 1, time: Date.now(), stop: !1 };
    i.push(o), t(
      Rt,
      () => {
        o = i[0], !o && t(fn, 1), o && (o.opacity = 1 - Math.min((Date.now() - o.time) / 1500, 1), o.opacity < 0.2 && (o.opacity = 0, o.stop = !0), t(fn, o.opacity));
      },
      () => !o?.stop
    );
  });
  function a() {
    const o = document.createElement("div");
    o.innerHTML = '<svg height="16" width="16" style="fill:white;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M108.8 469.333333C128 279.466667 279.466667 128 469.333333 108.8V64c0-23.466667 19.2-42.666667 42.666667-42.666667s42.666667 19.2 42.666667 42.666667v44.8c189.866667 19.2 341.333333 170.666667 360.533333 360.533333H960c23.466667 0 42.666667 19.2 42.666667 42.666667s-19.2 42.666667-42.666667 42.666667h-44.8c-19.2 189.866667-170.666667 341.333333-360.533333 360.533333V960c0 23.466667-19.2 42.666667-42.666667 42.666667s-42.666667-19.2-42.666667-42.666667v-44.8C279.466667 896 128 744.533333 108.8 554.666667H64c-23.466667 0-42.666667-19.2-42.666667-42.666667s19.2-42.666667 42.666667-42.666667h44.8zM469.333333 194.133333C326.4 213.333333 215.466667 326.4 196.266667 469.333333H234.666667c23.466667 0 42.666667 19.2 42.666666 42.666667s-19.2 42.666667-42.666666 42.666667H196.266667c19.2 142.933333 132.266667 256 275.2 273.066666V789.333333c0-23.466667 19.2-42.666667 42.666666-42.666666s42.666667 19.2 42.666667 42.666666v38.4C697.6 810.666667 810.666667 697.6 829.866667 554.666667H789.333333c-23.466667 0-42.666667-19.2-42.666666-42.666667s19.2-42.666667 42.666666-42.666667h40.533334C810.666667 326.4 697.6 213.333333 554.666667 194.133333V234.666667c0 23.466667-19.2 42.666667-42.666667 42.666666s-42.666667-19.2-42.666667-42.666666V194.133333z"></path></svg>', o.classList.add("css3d-focus-marker"), o.style.position = "absolute";
    const r = new we(o);
    return r.element.style.pointerEvents = "none", r.element.style.opacity = "1", r.visible = !1, t(V).add(r), r.onBeforeRender = () => t(bs), r;
  }
}
function xr(n) {
  const e = (o, r, l) => n.on(o, r, l), t = (o, ...r) => n.fire(o, ...r), s = /* @__PURE__ */ new Map(), i = document.createElement("div");
  i.classList.add("mark-warp"), document.body.appendChild(i);
  const a = new JA();
  a.setSize(innerWidth, innerHeight), a.domElement.style.position = "absolute", a.domElement.style.top = "0px", a.domElement.style.pointerEvents = "none", i.appendChild(a.domElement), e(ac, () => i), e(xi, () => a), e(Qi, () => document.body.removeChild(i)), e(Ft, () => a.render(t(V), t($)), !0), e(ct, (o) => {
    const r = o?.getMarkData?.()?.name || o?.name;
    r && s.set(r, new WeakRef(o));
  }), e(Ut, (o) => {
    const r = o?.getMarkData?.()?.name || o?.name;
    s.delete(r);
  }), e(Kt, (o) => s.get(o)?.deref()), e(rc, (o, r) => {
    const l = t(Kt, o);
    !l || !r || l.drawUpdate?.(r);
  }), e(cc, (o) => {
    const r = t(Kt, o);
    return r ? r.getMarkData?.() : {};
  }), e($t, (o) => {
    o !== void 0 && (t(F).markVisible = o), t(V).traverse((r) => r.isMark && (r.visible = t(F).markVisible)), t(j);
  }), e(ti, async () => {
    const o = [];
    t(V).traverse((l) => {
      if (l.isMark) {
        const A = l.getMarkData?.();
        A && o.push(A);
      }
    });
    const r = t(ve).meta || {};
    return o.length ? r.marks = o : delete r.marks, r.cameraInfo = t(ma), await t(je, r);
  }), e(Un, async () => {
    const o = [];
    t(V).traverse((l) => {
      if (l.isMark) {
        const A = l.getMarkData?.();
        A && o.push(A);
      }
    });
    const r = t(ve).meta || {};
    return o.length ? r.marks = o : delete r.marks, await t(je, r);
  }), e(Ga, async () => {
    const o = t(ve).meta || {};
    delete o.marks;
    const r = await t(je, o), l = [];
    return s.forEach((A) => l.push(A)), l.forEach((A) => A.deref()?.dispose?.()), t(j), r;
  }), e(za, async () => {
    t(V).traverse((r) => {
      r.isMark && r.getMarkData?.();
    });
    const o = t(ve).meta || {};
    return o.watermark = t(Ha) || "", await t(je, o);
  }), e(ni, (o) => {
    o.meterScale && (t(F).meterScale = o.meterScale, t(ce, { scale: `1 : ${t(F).meterScale} m` })), t(H).updateByOptions({ ...o, ...o.cameraInfo || {} }), (o.marks || []).forEach((l) => {
      if (l.type === "MarkSinglePoint") {
        const A = new Er(n, l);
        A.visible = !1, t(V).add(A);
      } else if (l.type === "MarkDistanceLine") {
        const A = new gi(n);
        A.draw(l), A.visible = !1, t(V).add(A);
      } else if (l.type === "MarkMultiLines") {
        const A = new fi(n);
        A.draw(l, !0), A.visible = !1, t(V).add(A);
      } else if (l.type === "MarkMultiPlans") {
        const A = new pi(n);
        A.draw(l, !0), A.visible = !1, t(V).add(A);
      } else if (l.type === "MarkCirclePlan") {
        const A = new br(n);
        A.draw(l), A.visible = !1, t(V).add(A);
      }
    }), t(cs, o.flyPositions || []), t(ls, o.flyTargets || []);
  }), e(lt, () => {
    const o = t(F);
    o.markMode = !1, t(vi), t(j);
  }), e(Ya, (o, r = !0) => {
    const l = o?.meterScale;
    if (l) {
      if (typeof l != "number" || l <= 0) {
        console.warn("meterScale is not a number or <= 0", o);
        return;
      }
      r && (t(F).meterScale = o.meterScale), t(ce, { scale: `1 : ${l} m` });
      for (const A of s.values()) {
        const c = A.deref();
        c && (c instanceof gi || c instanceof fi || c instanceof pi) && c.updateByMeterScale(l);
      }
    }
  }), e(Xs, (o) => {
    const r = new k().fromArray(o.slice(0, 3)), l = new k().fromArray(o.slice(-6, -3)), A = new k().fromArray(o.slice(-3)), c = r.distanceTo(A) < 1e-4, d = l.distanceTo(A) < 1e-4, h = new k(), p = c || d ? o.length / 3 - 1 : o.length / 3;
    for (let m = 0; m < p; m++)
      h.add(new k(o[m * 3], o[m * 3 + 1], o[m * 3 + 2]));
    return h.divideScalar(p), h;
  }), e(Js, (o) => {
    const r = [];
    for (let d = 0, h = o.length / 3; d < h; d++)
      r.push(new k(o[d * 3], o[d * 3 + 1], o[d * 3 + 2]));
    const l = r[0].distanceTo(r[r.length - 1]) < 1e-4, A = r[r.length - 2].distanceTo(r[r.length - 1]) < 1e-4;
    if ((l || A) && r.pop(), r.length < 3) return 0;
    let c = 0;
    for (let d = 0, h = r.length - 2; d < h; d++)
      c += t(ps, r[0], r[d + 1], r[d + 2], t(F).meterScale);
    return c;
  }), e(fa, (o, r) => {
    let l = 0;
    for (let A = 0, c = o.length - 2; A < c; A++)
      l += t(ps, o[0], o[A + 1], o[A + 2], r);
    return l;
  }), e(ps, (o, r, l, A) => {
    const c = o.distanceTo(r) * A, d = r.distanceTo(l) * A, h = l.distanceTo(o) * A, p = (c + d + h) / 2;
    return Math.sqrt(p * (p - c) * (p - d) * (p - h));
  });
}
function Qr(n) {
  const e = (f, ...u) => n.fire(f, ...u), t = (f, u, v) => n.on(f, u, v), s = [], i = [];
  let a = !1, o = !1;
  t(es, () => a = !1), t(no, () => a = !0), t(to, () => s), t(lc, () => {
    const f = [];
    for (let u = 0, v = s.length; u < v; u++)
      f.push(...s[u].toArray());
    return f;
  }), t(Ac, () => {
    const f = [];
    for (let u = 0, v = i.length; u < v; u++)
      f.push(...i[u].toArray());
    return f;
  }), t(cs, (f) => {
    for (let u = 0, v = f.length / 3 | 0; u < v; u++)
      s[u] = new k(f[u * 3 + 0], f[u * 3 + 1], f[u * 3 + 2]);
  }), t(ls, (f) => {
    for (let u = 0, v = f.length / 3 | 0; u < v; u++)
      i[u] = new k(f[u * 3 + 0], f[u * 3 + 1], f[u * 3 + 2]);
  }), t(rn, () => {
    const f = e(H);
    s.push(f.object.position.clone()), i.push(f.target.clone());
  }), t(ln, () => {
    s.length = 0, i.length = 0;
  }), t(cn, async (f = !0) => {
    const u = e(ve)?.meta || e(ki);
    if (u.scenes) {
      const x = u;
      if (s.length) {
        const M = [], D = [];
        for (let S = 0, P = s.length; S < P; S++)
          M.push(...s[S].toArray()), D.push(...i[S].toArray());
        x.flyPositions = M, x.flyTargets = D;
      } else
        delete x.flyPositions, delete x.flyTargets;
      const R = e(H);
      x.position = R.object.position.toArray(), x.lookAt = R.target.toArray(), f && await e(je, x);
    } else {
      const x = u;
      if (s.length) {
        const R = [], M = [];
        for (let D = 0, S = s.length; D < S; D++)
          R.push(...s[D].toArray()), M.push(...i[D].toArray());
        x.flyPositions = R, x.flyTargets = M;
      } else
        delete x.flyPositions, delete x.flyTargets;
      f && await e(je, x);
    }
    const v = (u.scenes, u);
    if (s.length) {
      const x = [], R = [];
      for (let M = 0, D = s.length; M < D; M++)
        x.push(...s[M].toArray()), R.push(...i[M].toArray());
      v.flyPositions = x, v.flyTargets = R;
    } else
      delete v.flyPositions, delete v.flyTargets;
    f && await e(je, v);
  }), t(Oa, () => {
    o || (o = !0) && e(ot, !0);
  });
  let r = 0;
  const l = 125 * 1e3;
  let A = l, c = 0, d = 0, h = 0, p = 0, m = 1, y, g;
  t(ot, (f) => {
    if (r = 0, A = l, c = Date.now(), d = 0, h = c, p = 0, m = 1, y = null, g = null, !s.length || !f && !e(H).autoRotate) return;
    const u = e(H), v = [u.object.position.clone()], x = [u.target.clone()], R = e(to) || [];
    for (let M = 0, D = Math.min(R.length, 1e3); M < D; M++)
      R[M] && v.push(R[M]), i[M] && x.push(i[M]);
    y = new Gi(v), y.closed = !1, g = new Gi(x), g.closed = !1, e(no), e(me, !1);
  }), t(gc, () => {
    d = d || Date.now();
  }), t(wc, (f, u) => {
    u && (p = u * l), d && (h = Date.now(), d = 0), a || e(ot, !0), m = f;
  }), t(
    jn,
    () => {
      const f = e(H);
      if (d || (p += m * (Date.now() - h), h = Date.now(), (p < 0 || p > A) && e(es), !a || !y || !g)) return;
      r = p / A;
      const u = y.getPoint(r), v = g.getPoint(r);
      f.object.position.set(u.x, u.y, u.z), f.target.set(v.x, v.y, v.z);
    },
    !0
  );
}
class yd {
  constructor(e = {}) {
    this.disposed = !1, this.needUpdate = !0, console.info("Reall3dViewer", ds), this.init(Ko(e)), !e.disableDropLocalFile && this.enableDropLocalFile();
  }
  init(e) {
    const t = this;
    e.position = e.position ? [...e.position] : [0, -5, 15], e.lookAt = e.lookAt ? [...e.lookAt] : [0, 0, 0], e.lookUp = e.lookUp ? [...e.lookUp] : [0, -1, 0];
    const s = IA(e), i = e.scene = e.scene || new da();
    i.background = new yi(e.background), wA(e);
    const a = e.controls = new WA(e);
    a.updateByOptions(e);
    const o = a.object, r = new As();
    e.viewerEvents = r, t.events = r;
    const l = (p, m, y) => r.on(p, m, y), A = (p, ...m) => r.fire(p, ...m);
    l(er, () => t.metaMatrix), l(F, () => e), l(_t, () => s.domElement), l(qt, () => s), l(V, () => i), l(H, () => a), l($, () => o), l(Ee, () => e.bigSceneMode), l(oc, (p) => e.pointcloudMode = p);
    const c = [];
    l(j, () => {
      for (t.needUpdate = !0; c.length; ) c.pop().stop = !0;
      let p = { count: 0, stop: !1 };
      c.push(p), A(
        ha,
        () => {
          !t.disposed && (t.needUpdate = !0), p.count++ >= 600 && (p.stop = !0);
        },
        () => !t.disposed && (A(Mi) || !p.stop),
        10
      );
    }), Li(r), CA(r), _i(r), ad(r), xr(r), jA(r), Mr(r), rd(r), Qr(r), t.splatMesh = new Le(EA(e)), l(ve, () => t.splatMesh), i.add(t.splatMesh), MA(r), l(xs, (p) => {
      e.qualityLevel = p, t.splatMesh.options({ qualityLevel: p, renderer: void 0, scene: void 0 });
    }), l(Qs, (p) => {
      e.sortType = p, t.splatMesh.options({ sortType: p, renderer: void 0, scene: void 0 });
    }), i.add(new ua("#ffffff", 2)), s.setAnimationLoop(t.update.bind(t)), l(eo, () => {
      if (!(A(F).useCustomControl === !0)) {
        const y = a.update();
        Math.random() < 1e-3 && a.autoRotate;
      }
      !t.needUpdate && A(bi) && A(j);
    }), l(kt, () => A(rs), !0), l(kt, () => A(eo), !0);
    let d = performance.now();
    l(
      Ft,
      () => {
        try {
          const p = performance.now();
          if (!t.needUpdate || p - d < (Ce ? 25 : e.qualityLevel > 5 ? 1 : 18)) return;
          t.needUpdate = !1, d = p, s.render(i, o), A(Qe) && A(os);
        } catch (p) {
          console.warn(p.message);
        }
      },
      !0
    ), l(as, () => t.dispose()), l(Xa, () => console.info(JSON.stringify(A(ve).meta || {}, null, 2)));
    let h = "";
    l(pn, (p = "") => {
      h = p, t.splatMesh.fire(Bi, h, !0);
    }), l(Ha, () => h), A(ce, { scale: `1 : ${A(F).meterScale} m` }), t.initGsApi();
  }
  /**
   * 允许拖拽本地文件进行渲染
   */
  enableDropLocalFile() {
    const e = this;
    document.addEventListener("dragover", function(t) {
      t.preventDefault(), t.stopPropagation();
    }), document.addEventListener("drop", async function(t) {
      t.preventDefault(), t.stopPropagation();
      let s = t.dataTransfer.files[0];
      if (!s) return;
      let i, a = !1;
      if (s.name.endsWith(".spx"))
        i = "spx";
      else if (s.name.endsWith(".splat"))
        i = "splat";
      else if (s.name.endsWith(".ply"))
        i = "ply";
      else if (s.name.endsWith(".spz"))
        i = "spz";
      else if (s.name.endsWith(".sog"))
        i = "sog";
      else if (s.name.endsWith(".obj"))
        i = "obj";
      else if (s.name.endsWith(".scene.json"))
        a = !0;
      else
        return console.error("unsupported format:", s.name);
      const o = URL.createObjectURL(s), r = e.events.fire(F);
      r.bigSceneMode = !1, r.pointcloudMode = !0, r.debugMode = !0, r.autoRotate = i !== "obj", r.maxRenderCountOfPc = 1024 * 1e4, r.qualityLevel = 9, r.disableTransitionEffectOnLoad = !1, e.reset(r), a ? await e.addScene(o) : await e.addModel({ url: o, format: i }), URL.revokeObjectURL(o);
    });
  }
  /**
   * 刷新
   */
  update() {
    const e = this;
    if (e.disposed) return;
    const t = (s, ...i) => e.events.fire(s, ...i);
    t(kt), t(Ft), t(jn);
  }
  // 开发调试用临时接口
  fire(e, t, s) {
    const i = this;
    if (e === 1 && i.splatMesh.fire(Zs, t), e === 2 && i.events.fire(rn), e === 3 && i.events.fire(ot, !0), e === 4 && i.events.fire(ln), e === 5 && i.events.fire(cn), e === 6 && i.events.fire(Un), e === 7 && i.events.fire(Ga), e === 8 && (async () => {
      let a = await i.splatMesh.fire(Di);
      t && (a = i.splatMesh.fire(ja) + t), i.splatMesh.fire(Gn, a);
    })(), e === 9)
      if (!t)
        i.events.fire(xs, Be.Default5);
      else {
        const a = i.events.fire(F).qualityLevel;
        i.events.fire(xs, Math.max(Be.L1, Math.min(a + t, Be.L9)));
      }
    if (e === 10)
      if (!t)
        i.events.fire(Qs, tn.Default1);
      else {
        const a = [1, 2010, 2011, 2012, 2112], o = i.events.fire(F).sortType;
        let r = a.indexOf(o) + t;
        r = Math.max(0, Math.min(r, a.length - 1)), i.events.fire(Qs, a[r]);
      }
  }
  /**
   * 设定或者获取最新配置项
   * @param opts 配置项
   * @returns 最新配置项
   */
  options(e) {
    const t = this;
    if (t.disposed) return {};
    const s = (r, ...l) => t.events.fire(r, ...l);
    let i;
    if (s(V).traverse((r) => !i && r instanceof Le && (i = r.options())), e) {
      const r = s(F);
      e.autoRotate !== void 0 && (r.autoRotate = e.autoRotate, s(H).autoRotate = e.autoRotate), e.pointcloudMode !== void 0 && s(qn, e.pointcloudMode), e.lightFactor !== void 0 && s(jt, e.lightFactor), e.maxRenderCountOfMobile && (r.maxRenderCountOfMobile = e.maxRenderCountOfMobile), e.maxRenderCountOfPc && (r.maxRenderCountOfPc = e.maxRenderCountOfPc), e.debugMode !== void 0 && (r.debugMode = e.debugMode), e.qualityLevel !== void 0 && (r.qualityLevel = e.qualityLevel) && this.splatMesh.options({ qualityLevel: e.qualityLevel }), e.sortType !== void 0 && (r.sortType = e.sortType) && this.splatMesh.options({ sortType: e.sortType }), e.cameraMoveSpeed !== void 0 && (s(F).cameraMoveSpeed = e.cameraMoveSpeed), e.useCustomControl !== void 0 && (s(F).useCustomControl = e.useCustomControl), e.useCustomControl !== void 0 && s(H).updateControlMode(s(F)), e.markType !== void 0 && (s(F).markType = e.markType), e.markVisible !== void 0 && s($t, e.markVisible), e.meterScale !== void 0 && (r.meterScale = e.meterScale), e.markMode !== void 0 && (r.markMode = e.markMode, !e.markMode && s(vi), s(H).autoRotate = s(F).autoRotate = !1), e.disableProgressiveLoading !== void 0 && (s(F).disableProgressiveLoading = e.disableProgressiveLoading), e.disableStreamLoading !== void 0 && (s(F).disableStreamLoading = e.disableStreamLoading), e.onPerformanceUpdate !== void 0 && (s(F).onPerformanceUpdate = e.onPerformanceUpdate);
    }
    return s(H).updateByOptions(e), s(ce, { scale: `1 : ${s(F).meterScale} m` }), Object.assign({ ...s(F) }, i);
  }
  /**
   * 重置
   */
  reset(e = {}) {
    const t = this;
    t.dispose(), t.disposed = !1, t.init(Ko(e));
  }
  /**
   * 光圈过渡切换显示
   * @returns
   */
  switchDeiplayMode() {
    const e = this;
    e.disposed || e.events.fire($n);
  }
  /**
   * 添加场景
   * @param sceneUrl 场景地址
   */
  async addScene(e) {
    const t = this;
    if (t.disposed) return;
    const s = (o, ...r) => t.events.fire(o, ...r);
    let i = {};
    try {
      const o = await fetch(e, { mode: "cors", credentials: "omit", cache: "reload" });
      if (o.status === 200)
        i = await o.json();
      else
        return console.error("scene file fetch failed, status:", o.status);
    } catch (o) {
      return console.error("scene file fetch failed", o.message);
    }
    if (!i.url) return console.error("missing model file url");
    if (!i.url.endsWith(".spx")) return console.error("The format is unsupported in the large scene mode", i.url);
    const a = { ...i, ...i.cameraInfo || {} };
    i.autoCut = Math.min(Math.max(i.autoCut || 0, 0), 50), a.bigSceneMode = i.autoCut > 1, t.reset({ ...a }), !a.bigSceneMode && delete i.autoCut, t.metaMatrix = i.transform ? new $e().fromArray(i.transform) : null, t.splatMesh.meta = i, Ce && (i.cameraInfo?.position || i.cameraInfo?.lookAt) && t.events.fire(H)._dollyOut(0.75), a.bigSceneMode ? (s(cs, i.flyPositions || []), s(ls, i.flyTargets || [])) : s(ni, i), await t.splatMesh.addModel({ url: i.url }, i), await s(pn, i.watermark), s(H).updateRotateAxis();
  }
  /**
   * 添加要渲染的高斯模型（小场景模式）
   * @param urlOpts 高斯模型链接或元数据文件链接或选项
   */
  async addModel(e) {
    const t = this;
    if (t.disposed) return;
    const s = (A, c, d) => this.events.on(A, c, d), i = (A, ...c) => t.events.fire(A, ...c);
    let a = "", o = { url: "" };
    if (Object.prototype.toString.call(e) === "[object String]" ? e.endsWith(".meta.json") ? a = e : o.url = e : o = e, !o.url && !a) return console.error("model url is empty");
    const r = i(F);
    r.bigSceneMode = !1;
    let l = {};
    if (!o.url.startsWith("blob:"))
      try {
        a = a || o.url.substring(0, o.url.lastIndexOf(".")) + ".meta.json";
        const A = await fetch(a, { mode: "cors", credentials: "omit", cache: "reload" });
        A.status === 200 ? l = await A.json() : console.warn("meta file fetch failed, status:", A.status);
      } catch (A) {
        console.warn("meta file fetch failed", A.message, o.url);
      }
    if (l.showWatermark = l.showWatermark !== !1, l.url = l.url || o.url, delete l.autoCut, !o.format)
      if (o.url = o.url || l.url, o.url.endsWith(".spx"))
        o.format = "spx";
      else if (o.url.endsWith(".splat"))
        o.format = "splat";
      else if (o.url.endsWith(".ply"))
        o.format = "ply";
      else if (o.url.endsWith(".spz"))
        o.format = "spz";
      else if (o.url.endsWith(".sog") || o.url.endsWith("/meta.json") || o.url == "meta.json")
        o.format = "sog";
      else if (o.url.endsWith(".obj"))
        o.format = "obj";
      else {
        console.error("unknow format!", o.url);
        return;
      }
    l.qualityLevel = l.qualityLevel || r.qualityLevel || Be.Default5, l.sortType = l.sortType || r.sortType || tn.Default1, s(ki, () => l), t.metaMatrix = l.transform ? new $e().fromArray(l.transform) : null, i(ni, l), this.options({ qualityLevel: l.qualityLevel, sortType: l.sortType }), o.format === "obj" ? (this.options({ autoRotate: !1 }), await i($a, o.url)) : (this.splatMesh.addModel(o, l), await i(pn, l.watermark)), Ce && i(H)._dollyOut?.(0.75);
  }
  /**
   * 根据需要暴露的接口
   */
  initGsApi() {
    const e = (c, ...d) => this.events.fire(c, ...d), t = () => {
      setTimeout(() => window.focus()), e(es), e(H).autoRotate = e(F).autoRotate = !e(F).autoRotate;
    }, s = (c = !0) => {
      setTimeout(() => window.focus()), c = !!c;
      const d = e(F);
      d.pointcloudMode !== c && (d.bigSceneMode ? e(qn, c) : e($n));
    }, i = (c = !0) => {
      setTimeout(() => window.focus()), e($t, !!c);
    }, a = (c) => {
      setTimeout(() => window.focus());
      const d = e(F);
      if (d.markMode) return !1;
      if (c === 1)
        d.markType = "point";
      else if (c === 2)
        d.markType = "lines";
      else if (c === 3)
        d.markType = "plans";
      else if (c === 4)
        d.markType = "distance";
      else
        return !1;
      return d.markMode = !0, e(me), !0;
    }, o = async (c) => (setTimeout(() => window.focus()), c ? (e(Kt, c)?.dispose(), e(j), await e(Un)) : !1), r = async (c, d = !0) => (e(Ya, c, d), e(Kt, c?.name)?.drawUpdate?.(c, d), e(j), d ? (setTimeout(() => window.focus()), await e(Un)) : !0), l = (c = !0) => {
      setTimeout(() => window.focus()), e(Zs, !!c);
    }, A = (c, d = !0) => {
      e(pn, c), d && e(za, c);
    };
    window.$api = { switchAutoRotate: t, changePointCloudMode: s, showMark: i, startMark: a, deleteMark: o, updateMark: r, showWaterMark: l, setWaterMark: A };
  }
  /**
   * 销毁渲染器不再使用
   */
  dispose() {
    const e = this;
    if (e.disposed) return;
    e.disposed = !0;
    const t = (a, ...o) => e.events.fire(a, ...o), s = t(qt), i = s.domElement;
    t(Ei), t(Ba), t(Qi), t(wi), t(H).dispose(), t(tt, t(V)), s.clear(), s.dispose(), i.parentElement.removeChild(i), e.splatMesh = null, e.events.clear(), e.events = null;
  }
  /**
   * 启用或禁用第一人称模式
   *
   * 第一人称模式提供类似FPS游戏的控制体验：
   * - 使用WASD键控制相机前后左右移动
   * - 使用箭头键控制相机上下移动
   * - 使用鼠标左键拖动来旋转视角
   *
   * @example
   * // 启用第一人称模式
   * viewer.enableFirstPersonMode(true);
   *
   * // 启用第一人称模式并设置较快的移动速度
   * viewer.enableFirstPersonMode(true, 0.01);
   *
   * // 禁用第一人称模式，恢复默认控制
   * viewer.enableFirstPersonMode(false);
   *
   * @param enable 是否启用第一人称模式，默认为true
   * @param cameraMoveSpeed 相机移动速度，值越大移动越快，默认为0.005
   * @returns 当前的ViewerOptions
   */
  enableFirstPersonMode(e = !0, t) {
    const s = this, i = (d, ...h) => s.events.fire(d, ...h), a = i($), o = i(H), r = a.position.clone();
    r.toArray();
    const l = new k();
    a.getWorldDirection(l);
    const A = {
      useCustomControl: e
    };
    t !== void 0 && (A.cameraMoveSpeed = t);
    const c = this.options(A);
    if (e) {
      a.position.copy(r);
      const d = r.clone().add(l);
      o.target.copy(d);
      const h = o.getDistance();
      o.minDistance = h, o.maxDistance = h, o.updateControlMode(i(F)), i(j), a.position.toArray();
    }
    return a.position.toArray(), c;
  }
}
function cd(n) {
  const e = (o, r, l) => n.on(o, r, l), t = (o, ...r) => n.fire(o, ...r), s = Ce ? 2 : 20, i = /* @__PURE__ */ new Map(), a = /* @__PURE__ */ new Map();
  e(zn, () => t(Qe) && i.set(Date.now(), 1)), e(Vn, () => t(Qe) && t(Dt, i)), e(os, () => t(Qe) && a.set(Date.now(), 1)), e(On, () => t(Qe) && t(Dt, a)), e(Dt, (o) => {
    let r = [], l = Date.now(), A = 0;
    for (const c of o.keys())
      l - c <= 1e3 ? A++ : r.push(c);
    return r.forEach((c) => o.delete(c)), Math.min(A, 60);
  }), e(de, () => {
    const o = t(V), r = t($), l = [];
    o?.traverse(function(A) {
      A.isWarpSplatMesh && A.splatMesh?.visible && l.push(A);
    }), l.sort((A, c) => r.position.distanceTo(A.position) - r.position.distanceTo(c.position));
    for (let A = 0; A < l.length; A++)
      l[A].splatMesh.boundBox.visible = A < 1;
    return window.splat = l[0]?.splatMesh, l[0]?.splatMesh;
  }), e(cr, () => {
    const o = t(V), r = t($), l = [];
    o?.traverse(function(A) {
      A.isWarpSplatMesh && l.push(A);
    }), l.sort((A, c) => r.position.distanceTo(A.position) - r.position.distanceTo(c.position));
    for (let A = 0; A < l.length; A++)
      l[A].active = A < s, l[A].splatMesh && (l[A].splatMesh.renderOrder = 1e3 - A);
  }), e(lr, () => {
    const o = t(V), r = [];
    o?.traverse((l) => r.push(l)), r.forEach((l) => {
      l.dispose ? l.dispose() : (l.geometry?.dispose?.(), l.material && l.material instanceof Array ? l.material.forEach((A) => A?.dispose?.()) : l.material?.dispose?.());
    }), o?.clear();
  }), e(ai, (o = 0.1) => {
    const r = t(de);
    r && r.visible && r.rotateOnAxis(new k(1, 0, 0), it.degToRad(o));
  }), e(ri, (o = 0.1) => {
    const r = t(de);
    r && r.visible && r.rotateOnAxis(new k(0, 1, 0), it.degToRad(o));
  }), e(ci, (o = 0.1) => {
    const r = t(de);
    r && r.visible && r.rotateOnAxis(new k(0, 0, 1), it.degToRad(o));
  }), e(fc, (o = 0.01) => {
    const r = t(de);
    r && r.visible && (r.position.x += o);
  }), e(rr, (o = 0.01) => {
    const r = t(de);
    r && r.visible && (r.position.y += o);
  }), e(pc, (o = 0.01) => {
    const r = t(de);
    r && r.visible && (r.position.z += o);
  }), e(mc, (o) => {
    const r = t(de);
    r && r.visible && o && r.position.copy(o), console.info(r, o);
  }), e(Cc, (o = 0.01) => {
    const r = t(de);
    r && r.visible && r.scale.set(r.scale.x + o, r.scale.y + o, r.scale.z + o);
  }), e(yc, () => {
    const o = t(de);
    o && (o.visible = !o.visible);
  }), e(Ic, async (o) => {
    if (!o && (o = t(de)), !o) return;
    const r = o.meta || {};
    return r.transform = o.matrix.toArray(), await t(je, JSON.stringify(r), o.meta.url);
  }), e(sr, () => {
    const o = t(F).root, r = new ra({
      antialias: !1,
      stencil: !0,
      alpha: !0,
      logarithmicDepthBuffer: !0,
      premultipliedAlpha: !1,
      precision: "highp",
      powerPreference: "high-performance"
    });
    return r.setSize(o.clientWidth, o.clientHeight), r.setPixelRatio(Math.min(devicePixelRatio, 2)), e(qt, () => r), e(_t, () => r.domElement), r;
  }), e(ir, () => {
    const o = t(F), r = new da(), l = o.background || "#dbf0ff";
    return r.background = new yi(l), r.fog = new zi(l, 0), e(V, () => r), r;
  }), e(or, () => {
    const r = t($), l = t(V), A = t(F), c = new HA(t($), A.root);
    c.screenSpacePanning = !1, c.minDistance = 0.1, c.maxDistance = 6e4, c.maxPolarAngle = 1.2, c.enableDamping = !0, c.dampingFactor = 0.07, c.zoomToCursor = !0;
    const d = new k().fromArray(A.minPan || [-2e4, 0.1, -6e4]), h = new k().fromArray(A.maxPan || [5e4, 1e4, 0]), p = new k();
    return c.addEventListener("change", () => {
      const m = Math.max(c.getPolarAngle(), 0.1), y = Math.max(c.getDistance(), 0.1);
      c.zoomSpeed = Math.max(Math.log(y), 0) + 0.5, r.far = it.clamp(y / m * 8, 100, 1e5), r.near = r.far / 1e3, r.updateProjectionMatrix(), l.fog instanceof zi && (l.fog.density = m / (y + 5) * 1 * 0.25), c.maxPolarAngle = Math.min(Math.pow(1e4 / y, 4), 1.2), p.copy(c.target), c.target.clamp(d, h), p.sub(c.target), r.position.sub(p);
    }), e(H, () => c), e(an, () => r.fov), e(Ue, (m = !1) => m ? r.position.clone() : r.position), e(Ve, (m = !1) => m ? c.target.clone() : c.target), e(Lt, (m = !1) => m ? r.up.clone() : r.up), c;
  }), e(ar, () => {
    const o = new Zr(16777215, 1);
    return o.position.set(0, 2e3, 1e3), o;
  }), window.addEventListener("beforeunload", () => t(as));
}
function ld(n) {
  let { root: e = "#map", debugMode: t } = n;
  e ? e = typeof e == "string" ? document.querySelector(e) || document.querySelector("#map") : e : e = document.querySelector("#map"), e || (e = document.createElement("div"), e.id = "map", (document.querySelector("#gsviewer") || document.querySelector("body")).appendChild(e));
  const s = { ...n };
  return s.root = e, s;
}
function Ad() {
  const n = new Vi.plugin.BingSource(), e = new Vi.TileMap({ imgSource: n, lon0: 90, minLevel: 2, maxLevel: 16 });
  return e.scale.set(10, 10, 10), e.rotateX(-Math.PI / 2), e.autoUpdate = !1, e;
}
class dd {
  constructor() {
    this.down = 0, this.move = !1, this.downTime = 0, this.isDbClick = !1, this.x = 0, this.y = 0, this.lastClickX = 0, this.lastClickY = 0, this.lastClickPointTime = 0, this.lastMovePoint = null, this.lastMovePointTime = 0;
  }
}
function ud(n) {
  const e = (M, D, S) => n.on(M, D, S), t = (M, ...D) => n.fire(M, ...D), s = t(_t);
  let i = /* @__PURE__ */ new Set(), a, o = new dd();
  e(rs, () => {
    if (!i.size) return;
    const M = t(F);
    if (!M.enableKeyboard) return i.clear();
    const D = M.editMode;
    if (i.has("KeyH")) {
      const S = t(de);
      S && (S.visible = !S.visible), i.clear();
    } else if (i.has("Equal"))
      if (D) {
        const S = t(de);
        if (!S || !S.visible) return i.clear();
        if (i.has("KeyX")) {
          const P = it.degToRad(0.1);
          S.rotateOnAxis(new k(1, 0, 0), P);
        } else
          S.scale.set(S.scale.x + 0.01, S.scale.y + 0.01, S.scale.z + 0.01);
      } else
        t(rn), i.clear();
    else if (i.has("Minus"))
      if (D) {
        const S = t(de);
        if (!S || !S.visible) return i.clear();
        if (i.has("KeyX")) {
          const P = it.degToRad(-0.1);
          S.rotateOnAxis(new k(1, 0, 0), P);
        } else
          S.scale.set(S.scale.x - 0.01, S.scale.y - 0.01, S.scale.z - 0.01);
      } else
        t(ln), i.clear();
    else if (i.has("KeyP"))
      t(ot, !0), i.clear();
    else if (i.has("ArrowUp")) {
      if (D) {
        const S = t(de);
        S && S.visible && (S.position.z += 0.1);
      }
    } else if (i.has("ArrowDown")) {
      if (D) {
        const S = t(de);
        S && S.visible && (S.position.z -= 0.1);
      }
    } else if (i.has("ArrowRight")) {
      if (D) {
        const S = t(de);
        S && S.visible && (S.position.x += 0.1);
      }
    } else if (i.has("ArrowLeft")) {
      if (D) {
        const S = t(de);
        S && S.visible && (S.position.x -= 0.1);
      }
    } else if (i.has("KeyU")) {
      if (D) {
        const S = t(de);
        if (S) {
          const P = S.meta || {};
          P.transform = S.matrix.toArray();
        }
      }
    } else i.has("KeyQ") ? D && t(ai, 0.1) : i.has("KeyW") ? D && t(ri, 0.1) : i.has("KeyE") ? D && t(ci, 0.1) : i.has("KeyA") ? D && t(ai, -0.1) : i.has("KeyS") ? D && t(ri, -0.1) : i.has("KeyD") ? D && t(ci, -0.1) : i.has("KeyY") ? D ? t(rr, i.has("ShiftLeft") || i.has("ShiftRight") ? -0.1 : 0.1) : (t(cn, !0), i.clear()) : i.has("KeyC") && console.info("position=", t(Ue).toArray(), "lookat=", t(Ve).toArray());
  });
  const r = (M) => {
    M.target.type !== "text" && (a || M.code === "F5" || (M.preventDefault(), i.add(M.code)));
  }, l = (M) => {
    M.target.type !== "text" && (a || i.clear());
  }, A = () => {
    i.clear();
  }, c = (M) => {
    parent && setTimeout(() => window.focus()), M.preventDefault();
  }, d = async (M) => {
    M.preventDefault();
  };
  let h, p, m, y;
  e(Vt, () => {
    h?.dispose(), p?.dispose(), m?.dispose(), y?.dispose(), h = null, p = null, m = null, y = null, o.lastMovePoint = null, t(lt);
  });
  const g = async (M) => {
    parent && setTimeout(() => window.focus()), M.preventDefault(), !a && (o.down = M.button === 2 ? 2 : 1, o.move = !1, o.isDbClick = Date.now() - o.downTime < 300, o.downTime = Date.now());
  }, f = async (M) => {
    M.preventDefault(), !a && o.down && (o.move = !0);
  }, u = async (M) => {
    M.preventDefault(), !a && (o.down = 0, o.move = !1);
  };
  function v(M) {
    M.preventDefault(), !a && (o.down = M.touches.length, o.down === 1 && (o.move = !1, o.x = M.touches[0].clientX, o.y = M.touches[0].clientY));
  }
  function x(M) {
    M.touches.length === 1 && (o.move = !0);
  }
  function R(M) {
    o.down === 1 && !o.move && t(Kn, o.x, o.y);
  }
  window.addEventListener("keydown", r), window.addEventListener("keyup", l), window.addEventListener("blur", A), window.addEventListener("wheel", c, { passive: !1 }), s.addEventListener("contextmenu", d), s.addEventListener("mousedown", g), s.addEventListener("mousemove", f), s.addEventListener("mouseup", u), s.addEventListener("touchstart", v, { passive: !1 }), s.addEventListener("touchmove", x, { passive: !1 }), s.addEventListener("touchend", R, { passive: !1 }), e(wi, () => {
    a = !0, window.removeEventListener("keydown", r), window.removeEventListener("keyup", l), window.removeEventListener("blur", A), window.removeEventListener("wheel", c), s.removeEventListener("contextmenu", d), s.removeEventListener("mousedown", g), s.removeEventListener("mousemove", f), s.removeEventListener("mouseup", u), s.removeEventListener("touchstart", v), s.removeEventListener("touchmove", x), s.removeEventListener("touchend", R);
  }), e(Kn, async (M, D) => await t(Jn, M, D));
}
var Tt = Object.freeze({
  Linear: Object.freeze({
    None: function(n) {
      return n;
    },
    In: function(n) {
      return n;
    },
    Out: function(n) {
      return n;
    },
    InOut: function(n) {
      return n;
    }
  }),
  Quadratic: Object.freeze({
    In: function(n) {
      return n * n;
    },
    Out: function(n) {
      return n * (2 - n);
    },
    InOut: function(n) {
      return (n *= 2) < 1 ? 0.5 * n * n : -0.5 * (--n * (n - 2) - 1);
    }
  }),
  Cubic: Object.freeze({
    In: function(n) {
      return n * n * n;
    },
    Out: function(n) {
      return --n * n * n + 1;
    },
    InOut: function(n) {
      return (n *= 2) < 1 ? 0.5 * n * n * n : 0.5 * ((n -= 2) * n * n + 2);
    }
  }),
  Quartic: Object.freeze({
    In: function(n) {
      return n * n * n * n;
    },
    Out: function(n) {
      return 1 - --n * n * n * n;
    },
    InOut: function(n) {
      return (n *= 2) < 1 ? 0.5 * n * n * n * n : -0.5 * ((n -= 2) * n * n * n - 2);
    }
  }),
  Quintic: Object.freeze({
    In: function(n) {
      return n * n * n * n * n;
    },
    Out: function(n) {
      return --n * n * n * n * n + 1;
    },
    InOut: function(n) {
      return (n *= 2) < 1 ? 0.5 * n * n * n * n * n : 0.5 * ((n -= 2) * n * n * n * n + 2);
    }
  }),
  Sinusoidal: Object.freeze({
    In: function(n) {
      return 1 - Math.sin((1 - n) * Math.PI / 2);
    },
    Out: function(n) {
      return Math.sin(n * Math.PI / 2);
    },
    InOut: function(n) {
      return 0.5 * (1 - Math.sin(Math.PI * (0.5 - n)));
    }
  }),
  Exponential: Object.freeze({
    In: function(n) {
      return n === 0 ? 0 : Math.pow(1024, n - 1);
    },
    Out: function(n) {
      return n === 1 ? 1 : 1 - Math.pow(2, -10 * n);
    },
    InOut: function(n) {
      return n === 0 ? 0 : n === 1 ? 1 : (n *= 2) < 1 ? 0.5 * Math.pow(1024, n - 1) : 0.5 * (-Math.pow(2, -10 * (n - 1)) + 2);
    }
  }),
  Circular: Object.freeze({
    In: function(n) {
      return 1 - Math.sqrt(1 - n * n);
    },
    Out: function(n) {
      return Math.sqrt(1 - --n * n);
    },
    InOut: function(n) {
      return (n *= 2) < 1 ? -0.5 * (Math.sqrt(1 - n * n) - 1) : 0.5 * (Math.sqrt(1 - (n -= 2) * n) + 1);
    }
  }),
  Elastic: Object.freeze({
    In: function(n) {
      return n === 0 ? 0 : n === 1 ? 1 : -Math.pow(2, 10 * (n - 1)) * Math.sin((n - 1.1) * 5 * Math.PI);
    },
    Out: function(n) {
      return n === 0 ? 0 : n === 1 ? 1 : Math.pow(2, -10 * n) * Math.sin((n - 0.1) * 5 * Math.PI) + 1;
    },
    InOut: function(n) {
      return n === 0 ? 0 : n === 1 ? 1 : (n *= 2, n < 1 ? -0.5 * Math.pow(2, 10 * (n - 1)) * Math.sin((n - 1.1) * 5 * Math.PI) : 0.5 * Math.pow(2, -10 * (n - 1)) * Math.sin((n - 1.1) * 5 * Math.PI) + 1);
    }
  }),
  Back: Object.freeze({
    In: function(n) {
      var e = 1.70158;
      return n === 1 ? 1 : n * n * ((e + 1) * n - e);
    },
    Out: function(n) {
      var e = 1.70158;
      return n === 0 ? 0 : --n * n * ((e + 1) * n + e) + 1;
    },
    InOut: function(n) {
      var e = 2.5949095;
      return (n *= 2) < 1 ? 0.5 * (n * n * ((e + 1) * n - e)) : 0.5 * ((n -= 2) * n * ((e + 1) * n + e) + 2);
    }
  }),
  Bounce: Object.freeze({
    In: function(n) {
      return 1 - Tt.Bounce.Out(1 - n);
    },
    Out: function(n) {
      return n < 1 / 2.75 ? 7.5625 * n * n : n < 2 / 2.75 ? 7.5625 * (n -= 1.5 / 2.75) * n + 0.75 : n < 2.5 / 2.75 ? 7.5625 * (n -= 2.25 / 2.75) * n + 0.9375 : 7.5625 * (n -= 2.625 / 2.75) * n + 0.984375;
    },
    InOut: function(n) {
      return n < 0.5 ? Tt.Bounce.In(n * 2) * 0.5 : Tt.Bounce.Out(n * 2 - 1) * 0.5 + 0.5;
    }
  }),
  generatePow: function(n) {
    return n === void 0 && (n = 4), n = n < Number.EPSILON ? Number.EPSILON : n, n = n > 1e4 ? 1e4 : n, {
      In: function(e) {
        return Math.pow(e, n);
      },
      Out: function(e) {
        return 1 - Math.pow(1 - e, n);
      },
      InOut: function(e) {
        return e < 0.5 ? Math.pow(e * 2, n) / 2 : (1 - Math.pow(2 - e * 2, n)) / 2 + 0.5;
      }
    };
  }
}), Xt = function() {
  return performance.now();
}, hd = (
  /** @class */
  (function() {
    function n() {
      for (var e = [], t = 0; t < arguments.length; t++)
        e[t] = arguments[t];
      this._tweens = {}, this._tweensAddedDuringUpdate = {}, this.add.apply(this, e);
    }
    return n.prototype.getAll = function() {
      var e = this;
      return Object.keys(this._tweens).map(function(t) {
        return e._tweens[t];
      });
    }, n.prototype.removeAll = function() {
      this._tweens = {};
    }, n.prototype.add = function() {
      for (var e, t = [], s = 0; s < arguments.length; s++)
        t[s] = arguments[s];
      for (var i = 0, a = t; i < a.length; i++) {
        var o = a[i];
        (e = o._group) === null || e === void 0 || e.remove(o), o._group = this, this._tweens[o.getId()] = o, this._tweensAddedDuringUpdate[o.getId()] = o;
      }
    }, n.prototype.remove = function() {
      for (var e = [], t = 0; t < arguments.length; t++)
        e[t] = arguments[t];
      for (var s = 0, i = e; s < i.length; s++) {
        var a = i[s];
        a._group = void 0, delete this._tweens[a.getId()], delete this._tweensAddedDuringUpdate[a.getId()];
      }
    }, n.prototype.allStopped = function() {
      return this.getAll().every(function(e) {
        return !e.isPlaying();
      });
    }, n.prototype.update = function(e, t) {
      e === void 0 && (e = Xt()), t === void 0 && (t = !0);
      var s = Object.keys(this._tweens);
      if (s.length !== 0)
        for (; s.length > 0; ) {
          this._tweensAddedDuringUpdate = {};
          for (var i = 0; i < s.length; i++) {
            var a = this._tweens[s[i]], o = !t;
            a && a.update(e, o) === !1 && !t && this.remove(a);
          }
          s = Object.keys(this._tweensAddedDuringUpdate);
        }
    }, n;
  })()
), mi = {
  Linear: function(n, e) {
    var t = n.length - 1, s = t * e, i = Math.floor(s), a = mi.Utils.Linear;
    return e < 0 ? a(n[0], n[1], s) : e > 1 ? a(n[t], n[t - 1], t - s) : a(n[i], n[i + 1 > t ? t : i + 1], s - i);
  },
  Utils: {
    Linear: function(n, e, t) {
      return (e - n) * t + n;
    }
  }
}, Br = (
  /** @class */
  (function() {
    function n() {
    }
    return n.nextId = function() {
      return n._nextId++;
    }, n._nextId = 0, n;
  })()
), Ci = new hd(), gd = (
  /** @class */
  (function() {
    function n(e, t) {
      this._isPaused = !1, this._pauseStart = 0, this._valuesStart = {}, this._valuesEnd = {}, this._valuesStartRepeat = {}, this._duration = 1e3, this._isDynamic = !1, this._initialRepeat = 0, this._repeat = 0, this._yoyo = !1, this._isPlaying = !1, this._reversed = !1, this._delayTime = 0, this._startTime = 0, this._easingFunction = Tt.Linear.None, this._interpolationFunction = mi.Linear, this._chainedTweens = [], this._onStartCallbackFired = !1, this._onEveryStartCallbackFired = !1, this._id = Br.nextId(), this._isChainStopped = !1, this._propertiesAreSetUp = !1, this._goToEnd = !1, this._object = e, typeof t == "object" ? (this._group = t, t.add(this)) : t === !0 && (this._group = Ci, Ci.add(this));
    }
    return n.prototype.getId = function() {
      return this._id;
    }, n.prototype.isPlaying = function() {
      return this._isPlaying;
    }, n.prototype.isPaused = function() {
      return this._isPaused;
    }, n.prototype.getDuration = function() {
      return this._duration;
    }, n.prototype.to = function(e, t) {
      if (t === void 0 && (t = 1e3), this._isPlaying)
        throw new Error("Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.");
      return this._valuesEnd = e, this._propertiesAreSetUp = !1, this._duration = t < 0 ? 0 : t, this;
    }, n.prototype.duration = function(e) {
      return e === void 0 && (e = 1e3), this._duration = e < 0 ? 0 : e, this;
    }, n.prototype.dynamic = function(e) {
      return e === void 0 && (e = !1), this._isDynamic = e, this;
    }, n.prototype.start = function(e, t) {
      if (e === void 0 && (e = Xt()), t === void 0 && (t = !1), this._isPlaying)
        return this;
      if (this._repeat = this._initialRepeat, this._reversed) {
        this._reversed = !1;
        for (var s in this._valuesStartRepeat)
          this._swapEndStartRepeatValues(s), this._valuesStart[s] = this._valuesStartRepeat[s];
      }
      if (this._isPlaying = !0, this._isPaused = !1, this._onStartCallbackFired = !1, this._onEveryStartCallbackFired = !1, this._isChainStopped = !1, this._startTime = e, this._startTime += this._delayTime, !this._propertiesAreSetUp || t) {
        if (this._propertiesAreSetUp = !0, !this._isDynamic) {
          var i = {};
          for (var a in this._valuesEnd)
            i[a] = this._valuesEnd[a];
          this._valuesEnd = i;
        }
        this._setupProperties(this._object, this._valuesStart, this._valuesEnd, this._valuesStartRepeat, t);
      }
      return this;
    }, n.prototype.startFromCurrentValues = function(e) {
      return this.start(e, !0);
    }, n.prototype._setupProperties = function(e, t, s, i, a) {
      for (var o in s) {
        var r = e[o], l = Array.isArray(r), A = l ? "array" : typeof r, c = !l && Array.isArray(s[o]);
        if (!(A === "undefined" || A === "function")) {
          if (c) {
            var d = s[o];
            if (d.length === 0)
              continue;
            for (var h = [r], p = 0, m = d.length; p < m; p += 1) {
              var y = this._handleRelativeValue(r, d[p]);
              if (isNaN(y)) {
                c = !1, console.warn("Found invalid interpolation list. Skipping.");
                break;
              }
              h.push(y);
            }
            c && (s[o] = h);
          }
          if ((A === "object" || l) && r && !c) {
            t[o] = l ? [] : {};
            var g = r;
            for (var f in g)
              t[o][f] = g[f];
            i[o] = l ? [] : {};
            var d = s[o];
            if (!this._isDynamic) {
              var u = {};
              for (var f in d)
                u[f] = d[f];
              s[o] = d = u;
            }
            this._setupProperties(g, t[o], d, i[o], a);
          } else
            (typeof t[o] > "u" || a) && (t[o] = r), l || (t[o] *= 1), c ? i[o] = s[o].slice().reverse() : i[o] = t[o] || 0;
        }
      }
    }, n.prototype.stop = function() {
      return this._isChainStopped || (this._isChainStopped = !0, this.stopChainedTweens()), this._isPlaying ? (this._isPlaying = !1, this._isPaused = !1, this._onStopCallback && this._onStopCallback(this._object), this) : this;
    }, n.prototype.end = function() {
      return this._goToEnd = !0, this.update(this._startTime + this._duration), this;
    }, n.prototype.pause = function(e) {
      return e === void 0 && (e = Xt()), this._isPaused || !this._isPlaying ? this : (this._isPaused = !0, this._pauseStart = e, this);
    }, n.prototype.resume = function(e) {
      return e === void 0 && (e = Xt()), !this._isPaused || !this._isPlaying ? this : (this._isPaused = !1, this._startTime += e - this._pauseStart, this._pauseStart = 0, this);
    }, n.prototype.stopChainedTweens = function() {
      for (var e = 0, t = this._chainedTweens.length; e < t; e++)
        this._chainedTweens[e].stop();
      return this;
    }, n.prototype.group = function(e) {
      return e ? (e.add(this), this) : (console.warn("tween.group() without args has been removed, use group.add(tween) instead."), this);
    }, n.prototype.remove = function() {
      var e;
      return (e = this._group) === null || e === void 0 || e.remove(this), this;
    }, n.prototype.delay = function(e) {
      return e === void 0 && (e = 0), this._delayTime = e, this;
    }, n.prototype.repeat = function(e) {
      return e === void 0 && (e = 0), this._initialRepeat = e, this._repeat = e, this;
    }, n.prototype.repeatDelay = function(e) {
      return this._repeatDelayTime = e, this;
    }, n.prototype.yoyo = function(e) {
      return e === void 0 && (e = !1), this._yoyo = e, this;
    }, n.prototype.easing = function(e) {
      return e === void 0 && (e = Tt.Linear.None), this._easingFunction = e, this;
    }, n.prototype.interpolation = function(e) {
      return e === void 0 && (e = mi.Linear), this._interpolationFunction = e, this;
    }, n.prototype.chain = function() {
      for (var e = [], t = 0; t < arguments.length; t++)
        e[t] = arguments[t];
      return this._chainedTweens = e, this;
    }, n.prototype.onStart = function(e) {
      return this._onStartCallback = e, this;
    }, n.prototype.onEveryStart = function(e) {
      return this._onEveryStartCallback = e, this;
    }, n.prototype.onUpdate = function(e) {
      return this._onUpdateCallback = e, this;
    }, n.prototype.onRepeat = function(e) {
      return this._onRepeatCallback = e, this;
    }, n.prototype.onComplete = function(e) {
      return this._onCompleteCallback = e, this;
    }, n.prototype.onStop = function(e) {
      return this._onStopCallback = e, this;
    }, n.prototype.update = function(e, t) {
      var s = this, i;
      if (e === void 0 && (e = Xt()), t === void 0 && (t = n.autoStartOnUpdate), this._isPaused)
        return !0;
      var a;
      if (!this._goToEnd && !this._isPlaying)
        if (t)
          this.start(e, !0);
        else
          return !1;
      if (this._goToEnd = !1, e < this._startTime)
        return !0;
      this._onStartCallbackFired === !1 && (this._onStartCallback && this._onStartCallback(this._object), this._onStartCallbackFired = !0), this._onEveryStartCallbackFired === !1 && (this._onEveryStartCallback && this._onEveryStartCallback(this._object), this._onEveryStartCallbackFired = !0);
      var o = e - this._startTime, r = this._duration + ((i = this._repeatDelayTime) !== null && i !== void 0 ? i : this._delayTime), l = this._duration + this._repeat * r, A = function() {
        if (s._duration === 0 || o > l)
          return 1;
        var y = Math.trunc(o / r), g = o - y * r, f = Math.min(g / s._duration, 1);
        return f === 0 && o === s._duration ? 1 : f;
      }, c = A(), d = this._easingFunction(c);
      if (this._updateProperties(this._object, this._valuesStart, this._valuesEnd, d), this._onUpdateCallback && this._onUpdateCallback(this._object, c), this._duration === 0 || o >= this._duration)
        if (this._repeat > 0) {
          var h = Math.min(Math.trunc((o - this._duration) / r) + 1, this._repeat);
          isFinite(this._repeat) && (this._repeat -= h);
          for (a in this._valuesStartRepeat)
            !this._yoyo && typeof this._valuesEnd[a] == "string" && (this._valuesStartRepeat[a] = // eslint-disable-next-line
            // @ts-ignore FIXME?
            this._valuesStartRepeat[a] + parseFloat(this._valuesEnd[a])), this._yoyo && this._swapEndStartRepeatValues(a), this._valuesStart[a] = this._valuesStartRepeat[a];
          return this._yoyo && (this._reversed = !this._reversed), this._startTime += r * h, this._onRepeatCallback && this._onRepeatCallback(this._object), this._onEveryStartCallbackFired = !1, !0;
        } else {
          this._onCompleteCallback && this._onCompleteCallback(this._object);
          for (var p = 0, m = this._chainedTweens.length; p < m; p++)
            this._chainedTweens[p].start(this._startTime + this._duration, !1);
          return this._isPlaying = !1, !1;
        }
      return !0;
    }, n.prototype._updateProperties = function(e, t, s, i) {
      for (var a in s)
        if (t[a] !== void 0) {
          var o = t[a] || 0, r = s[a], l = Array.isArray(e[a]), A = Array.isArray(r), c = !l && A;
          c ? e[a] = this._interpolationFunction(r, i) : typeof r == "object" && r ? this._updateProperties(e[a], o, r, i) : (r = this._handleRelativeValue(o, r), typeof r == "number" && (e[a] = o + (r - o) * i));
        }
    }, n.prototype._handleRelativeValue = function(e, t) {
      return typeof t != "string" ? t : t.charAt(0) === "+" || t.charAt(0) === "-" ? e + parseFloat(t) : parseFloat(t);
    }, n.prototype._swapEndStartRepeatValues = function(e) {
      var t = this._valuesStartRepeat[e], s = this._valuesEnd[e];
      typeof s == "string" ? this._valuesStartRepeat[e] = this._valuesStartRepeat[e] + parseFloat(s) : this._valuesStartRepeat[e] = this._valuesEnd[e], this._valuesEnd[e] = t;
    }, n.autoStartOnUpdate = !1, n;
  })()
);
Br.nextId;
var Je = Ci;
Je.getAll.bind(Je);
Je.removeAll.bind(Je);
Je.add.bind(Je);
Je.remove.bind(Je);
Je.update.bind(Je);
const oa = navigator.userAgent.includes("Mobi");
class fd extends Ie {
  constructor(e, t) {
    super(), this.isWarpSplatMesh = !0, this.lastActiveTime = Date.now(), this.active = !1, this.disposed = !1;
    const s = this;
    s.mapViewer = t, s.addScene(e), s.frustumCulled = !1;
  }
  async addScene(e) {
    const t = this, { renderer: s, scene: i, controls: a, tileMap: o } = t.mapViewer;
    fetch(e, { mode: "cors", credentials: "omit", cache: "reload" }).then((r) => r.ok ? r.json() : {}).then((r) => {
      const l = new $e();
      if (r.transform)
        l.fromArray(r.transform);
      else if (r.WGS84) {
        const m = o.geo2world(new k().fromArray(r.WGS84));
        l.makeTranslation(m.x, m.y, m.z), r.transform = l.toArray();
      }
      t.metaMatrix = l, r.autoCut && (r.autoCut = Math.min(Math.max(r.autoCut, 1), 50));
      const A = r.autoCut && r.autoCut > 1, c = !1, d = !1, h = r.showWatermark !== !1, p = { renderer: s, scene: i, controls: a, pointcloudMode: c, bigSceneMode: A, showWatermark: h, depthTest: d, mapMode: !0 };
      p.maxRenderCountOfMobile ?? (p.maxRenderCountOfMobile = p.bigSceneMode ? 128 * 10240 : 400 * 1e4), p.maxRenderCountOfPc ?? (p.maxRenderCountOfPc = p.bigSceneMode ? 320 * 1e4 : 400 * 1e4), p.debugMode = t.mapViewer.events.fire(F).debugMode, t.opts = p, t.meta = r, i.add(t), t.initCSS3DSprite(p), t.applyMatrix4(l);
    }).catch((r) => {
      console.error(r.message);
    });
  }
  async initCSS3DSprite(e) {
    const t = this, s = e.controls, i = document.createElement("div");
    i.innerHTML = `<div title="${t.meta.name}" style='flex-direction: column;align-items: center;display: flex;pointer-events: auto;margin-bottom: 20px;'>
                               <svg height="20" width="20" style="color:#eeee00;opacity:0.9;"><use href="#svgicon-point3" fill="currentColor" /></svg>
                            </div>`, i.classList.add("splatmesh-point"), i.style.position = "absolute", i.style.borderRadius = "4px", i.style.cursor = "pointer";
    let a = null;
    i.onclick = () => {
      if (a) return;
      const l = s.target.clone(), A = s.object.position.clone(), c = t.position.clone(), d = oa ? 6 : 2, p = l.clone().sub(A).normalize().clone(), m = c.clone().sub(p.multiplyScalar(d)), y = { x: A.x, y: A.y, z: A.z, tx: l.x, ty: l.y, tz: l.z }, g = { x: m.x, y: m.y, z: m.z, tx: c.x, ty: c.y, tz: c.z };
      a = new gd(y).to(g, 3500), a.easing(Tt.Sinusoidal.InOut).start().onUpdate(() => {
        s.object.position.set(y.x, y.y, y.z), s.target.set(y.tx, y.ty, y.tz);
      }).onComplete(() => {
        a = null;
      });
    }, i.oncontextmenu = (l) => l.preventDefault();
    const o = new we(i);
    o.element.style.pointerEvents = "none", o.visible = !1, o.applyMatrix4(t.metaMatrix), t.css3dTag = o, e.scene.add(o);
    const r = (l) => t.mapViewer.controls._onMouseWheel(l);
    i.addEventListener("wheel", r, { passive: !1 }), o.dispose = () => i.removeEventListener("wheel", r), t.onBeforeRender = () => {
      a?.update();
      const l = oa ? 60 : 30, A = 100, c = t.position.distanceTo(t.mapViewer.controls.object.position);
      if (c > l) {
        t.css3dTag.visible = t.opts.controls.object.position.y > 2;
        let d = 2e-3 * c;
        o.scale.set(d, d, d), t.css3dTag.visible = s.object.position.y < 10 ? c < A : !0, t.splatMesh && (t.splatMesh.visible = !1);
      } else {
        if (!t.active) {
          t.splatMesh && (t.splatMesh.visible = !1);
          let d = 2e-3 * c;
          o.scale.set(d, d, d), t.css3dTag.visible = !0, t.splatMesh?.boundBox && (t.splatMesh.boundBox.visible = !1);
          return;
        }
        if (t.lastActiveTime = Date.now(), t.css3dTag.visible = !1, t.splatMesh)
          t.splatMesh.visible = !0;
        else {
          const d = t.meta, h = { ...t.opts };
          d.autoCut && (h.bigSceneMode = !0);
          const p = new Le(h);
          t.splatMesh = p, t.opts.scene.add(p), p.meta = d;
          const m = d.watermark || d.name || "";
          d.showWatermark = d.showWatermark !== !1, p.fire(Bi, m, !0, !1), p.addModel({ url: d.url }, d);
        }
        t.splatMesh.meta.showBoundBox && (t.splatMesh.boundBox.visible = !0);
      }
    }, t.onAfterRender = () => {
      t.splatMesh && (!t.active || Date.now() - t.lastActiveTime > 60 * 1e3) && setTimeout(() => {
        t.splatMesh?.dispose(), t.splatMesh = null;
      }, 5);
    };
  }
  /**
   * 销毁
   */
  dispose() {
    const e = this;
    e.disposed || (e.disposed = !0, e.opts.scene.remove(e.css3dTag), e.splatMesh?.dispose(), e.meta = null, e.splatMesh = null, e.opts = null, e.css3dTag = null, e.mapViewer = null, e.metaMatrix = null);
  }
}
class Id extends jr {
  constructor(e = {}) {
    console.info("Reall3dMapViewer", ds), super(), this.clock = new qr(), this.updateTime = 0, this.disposed = !1;
    const t = this, s = new As();
    t.events = s;
    const i = (r, l, A) => s.on(r, l, A), a = (r, ...l) => s.fire(r, ...l);
    t.tileMap = Ad();
    const o = ld(e);
    i(F, () => o), Li(s), _i(s), cd(s), Mr(s), Qr(s), t.camera = new ca(60, 1, 0.01, 100), i($, () => t.camera), t.container = o.root, t.renderer = a(sr), t.scene = a(ir), t.controls = a(or), t.ambLight = new ua(16777215, 1), t.scene.add(t.ambLight), t.dirLight = a(ar), t.scene.add(t.dirLight), t.scene.add(t.tileMap), t.container.appendChild(t.renderer.domElement), xr(s), ud(s), window.addEventListener("resize", t.resize.bind(t)), t.resize(), t.renderer.setAnimationLoop(t.animate.bind(t)), Ce && t.controls._dollyOut?.(0.75), i(as, () => t.dispose()), i(
      kt,
      () => {
        a(os), t.controls.update(), a(cr), a(rs);
      },
      !0
    ), i(
      Ft,
      () => {
        t.tileMap.update(t.camera);
        try {
          t.renderer.render(t.scene, t.camera);
        } catch (r) {
          console.warn(r.message);
        }
      },
      !0
    ), i(
      jn,
      () => {
        t.dispatchEvent({ type: "update", delta: t.clock.getDelta() }), t.updateTime = Date.now(), a(Qe) && a(ce, {
          fps: a(Vn),
          realFps: a(On),
          fov: a(an),
          position: a(mt, a(Ue)),
          lookAt: a(mt, a(Ve)),
          lookUp: a(mt, a(Lt))
        });
      },
      !0
    );
  }
  /**
   * 打开地图场景
   * @param 场景索引文件地址
   */
  addScenes(e) {
    const t = (a, o, r) => this.events.on(a, o, r), s = (a, ...o) => this.events.fire(a, ...o), i = this;
    fetch(e, { mode: "cors", credentials: "omit", cache: "reload" }).then((a) => a.ok ? a.json() : {}).then((a) => {
      const o = new k().fromArray(a.position || [17e3, 3e4, -35e3]), r = new k().fromArray(a.lookAt || [17e3, 0, -35e3]);
      i.controls.object.position.copy(o), i.controls.target.copy(r), i.dirLight.target.position.copy(r), t(ki, () => a), s(cs, a.flyPositions || []), s(ls, a.flyTargets || []);
      const l = /* @__PURE__ */ new Set();
      for (let A of a.scenes)
        l.has(A) || (new fd(A, i), l.add(A));
    }).catch((a) => {
      console.error(a.message);
    });
  }
  // 开发调试用临时接口
  fire(e, t, s) {
    const i = this;
    e === 2 && i.events.fire(rn), e === 3 && i.events.fire(ot, !0), e === 4 && i.events.fire(ln), e === 5 && i.events.fire(cn);
  }
  resize() {
    const e = this;
    if (e.disposed) return;
    const { width: t, height: s, top: i, left: a } = e.container.getBoundingClientRect();
    e.renderer.setPixelRatio(Math.min(devicePixelRatio, 2)), e.renderer.setSize(t, s), e.camera.aspect = t / s, e.camera.updateProjectionMatrix();
    const o = e.events.fire(xi);
    o.setSize(t, s), o.domElement.style.position = "absolute", o.domElement.style.left = `${a}px`, o.domElement.style.top = `${i}px`;
  }
  animate() {
    const e = this, t = (s, ...i) => e.events.fire(s, ...i);
    t(zn), Date.now() - e.updateTime > 17 && (t(kt), t(Ft), t(jn));
  }
  /**
   * 销毁
   */
  dispose() {
    const e = this;
    if (e.disposed) return;
    e.disposed = !0;
    const t = e.renderer.domElement;
    e.events.fire(Qi), e.events.fire(lr), e.renderer.clear(), e.renderer.dispose(), e.events.clear(), e.scene = null, e.renderer = null, e.camera = null, e.controls = null, e.ambLight = null, e.dirLight = null, e.container.removeChild(t), e.container.classList.add("hidden"), e.container = null, e.clock = null, e.events = null, e.tileMap = null, document.querySelector("#gsviewer .debug.dev-panel")?.classList?.remove("map");
  }
}
export {
  Id as Reall3dMapViewer,
  yd as Reall3dViewer,
  Le as SplatMesh
};
