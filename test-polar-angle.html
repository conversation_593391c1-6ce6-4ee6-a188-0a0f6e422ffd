<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Polar Angle Limits</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        #gsviewer {
            width: 100vw;
            height: 100vh;
        }
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        .controls button {
            margin: 5px;
            padding: 5px 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="gsviewer"></div>
    
    <div class="controls">
        <h3>Camera Mode Test</h3>
        <button onclick="toggleCustomControl()">Toggle Custom Control</button>
        <button onclick="resetCamera()">Reset Camera</button>
        <div>
            <label>
                <input type="checkbox" id="customControlCheckbox"> Custom Control Mode
            </label>
        </div>
    </div>
    
    <div class="info">
        <div>Current Mode: <span id="currentMode">Orbit</span></div>
        <div>Polar Angle Limits: <span id="polarLimits">0° to 90°</span></div>
        <div>Instructions:</div>
        <div>• In Orbit mode: Camera should NOT go below horizontal (90°)</div>
        <div>• In Custom mode: Camera can look up/down freely</div>
        <div>• Try dragging to test the limits</div>
    </div>

    <script type="module">
        import { Reall3dViewer } from './dist/assets/entry-B1caw4BZ.js';

        let viewer;
        let isCustomControl = false;

        // Initialize viewer
        function initViewer() {
            viewer = new Reall3dViewer({
                position: [0, -5, 15],
                lookAt: [0, 0, 0],
                lookUp: [0, -1, 0],
                enableRotate: true,
                enableZoom: true,
                enablePan: true,
                autoRotate: false,
                // Test the polar angle limits
                minPolarAngle: 0,           // 0 degrees (horizontal up)
                maxPolarAngle: Math.PI / 2, // 90 degrees (horizontal down)
                useCustomControl: false
            });

            // Create a simple test scene with a cube
            createTestScene();
            
            updateUI();
        }

        function createTestScene() {
            // This would normally load a 3D Gaussian Splatting model
            // For testing purposes, we'll just use the viewer as is
            console.log('Test scene created');
        }

        function toggleCustomControl() {
            isCustomControl = !isCustomControl;
            
            viewer.options({
                useCustomControl: isCustomControl
            });
            
            updateUI();
        }

        function resetCamera() {
            viewer.options({
                position: [0, -5, 15],
                lookAt: [0, 0, 0],
                lookUp: [0, -1, 0]
            });
        }

        function updateUI() {
            const checkbox = document.getElementById('customControlCheckbox');
            const currentMode = document.getElementById('currentMode');
            const polarLimits = document.getElementById('polarLimits');
            
            checkbox.checked = isCustomControl;
            currentMode.textContent = isCustomControl ? 'Custom (FPS)' : 'Orbit';
            polarLimits.textContent = isCustomControl ? 'No limits (±90°)' : '0° to 90°';
        }

        // Global functions
        window.toggleCustomControl = toggleCustomControl;
        window.resetCamera = resetCamera;

        // Initialize when page loads
        initViewer();
    </script>
</body>
</html>
