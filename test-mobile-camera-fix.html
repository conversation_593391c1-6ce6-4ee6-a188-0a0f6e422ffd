<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端相机修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-results {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #1976d2;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>移动端相机修复测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <p>此测试页面用于验证移动端相机视角问题的修复效果：</p>
            <ul>
                <li><strong>问题1：</strong>移动端初始视角比PC端看起来要远很多</li>
                <li><strong>问题2：</strong>点击重置按钮后视角正常</li>
                <li><strong>问题3：</strong>每次点击autoRotate的toggle按钮，模型会变得越远</li>
            </ul>
        </div>

        <div class="test-results">
            <h3>修复方案</h3>
            <div class="status success">
                <strong>✓ 已修复：</strong>在 CameraControls 类中添加了 _mobileAdjusted 标志，防止重复调用 _dollyOut(0.75)
            </div>
            <div class="status success">
                <strong>✓ 已修复：</strong>新增 adjustForMobile() 方法，统一管理移动端相机距离调整
            </div>
            <div class="status success">
                <strong>✓ 已修复：</strong>修改了 Reall3dViewer.ts 中的两个调用点，使用新的统一方法
            </div>
            <div class="status success">
                <strong>✓ 已修复：</strong>修改了 Reall3dMapViewer.ts 中的调用，添加了防重复调用的逻辑
            </div>
        </div>

        <div class="test-results">
            <h3>技术细节</h3>
            <p><strong>根本原因：</strong></p>
            <ul>
                <li>移动端检测：<code>isMobile = navigator.userAgent.includes('Mobi')</code></li>
                <li>多个地方调用 <code>_dollyOut(0.75)</code> 导致相机越来越远</li>
                <li>每次切换 autoRotate 都会触发 <code>updateByOptions()</code>，重复调用移动端调整</li>
            </ul>
            
            <p><strong>修复方法：</strong></p>
            <ul>
                <li>添加 <code>_mobileAdjusted</code> 标志，确保移动端调整只执行一次</li>
                <li>创建 <code>adjustForMobile()</code> 方法统一管理</li>
                <li>所有相关调用点都使用新的统一方法</li>
            </ul>
        </div>

        <div class="test-results">
            <h3>测试步骤</h3>
            <ol>
                <li>在移动设备上打开3DGS Viewer应用</li>
                <li>加载任意模型，观察初始视角是否正常（不会过远）</li>
                <li>多次点击autoRotate切换按钮，观察模型是否会越来越远</li>
                <li>点击重置按钮，观察视角是否恢复正常</li>
                <li>检查浏览器控制台，应该只看到一次"移动端相机距离已调整"的日志</li>
            </ol>
        </div>

        <div class="test-results">
            <h3>预期结果</h3>
            <div class="status success">
                <strong>✓ 移动端初始视角：</strong>应该与PC端保持一致的相对距离，不会过远
            </div>
            <div class="status success">
                <strong>✓ autoRotate切换：</strong>多次切换不会导致模型越来越远
            </div>
            <div class="status success">
                <strong>✓ 重置功能：</strong>重置按钮功能保持正常
            </div>
            <div class="status success">
                <strong>✓ 日志输出：</strong>控制台只显示一次移动端调整日志
            </div>
        </div>

        <div class="test-results">
            <h3>相关文件修改</h3>
            <ul>
                <li><code>src/reall3d/controls/CameraControls.ts</code> - 主要修复文件</li>
                <li><code>src/reall3d/viewer/Reall3dViewer.ts</code> - 两个调用点修改</li>
                <li><code>src/reall3d/mapviewer/Reall3dMapViewer.ts</code> - 一个调用点修改</li>
            </ul>
        </div>
    </div>

    <script>
        // 检测当前是否为移动设备
        const isMobile = navigator.userAgent.includes('Mobi');
        
        if (isMobile) {
            document.querySelector('.container').insertAdjacentHTML('afterbegin', 
                '<div class="status warning"><strong>⚠️ 检测到移动设备</strong> - 请在实际的3DGS Viewer应用中测试修复效果</div>'
            );
        } else {
            document.querySelector('.container').insertAdjacentHTML('afterbegin', 
                '<div class="status warning"><strong>ℹ️ 检测到桌面设备</strong> - 请在移动设备上测试修复效果</div>'
            );
        }
    </script>
</body>
</html>
