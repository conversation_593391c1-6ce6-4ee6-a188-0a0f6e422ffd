<template>
  <div class="viewer-container">
    <!-- 3DGS Viewer容器 -->
    <div id="gsviewer" ref="viewerRef" class="viewer-canvas"></div>

    <!-- 控制按钮区域 -->
    <div class="control-buttons">
      <button @click="resetCameraAndRestartTour" class="reset-camera-btn" title="重置相机">
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <path d="M1 4v6h6" />
          <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" />
        </svg>
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>正在加载模型...</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-overlay">
      <div class="error-message">
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button @click="retry" class="retry-btn">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, computed } from 'vue'
import { useViewer } from '@/composables/useViewer'
import { getDecodedModelUrl } from '@/utils/url'
import type { ViewerConfig, ProjectConfig, CameraConfig } from '@/types/project'
import { SceneLoader } from '@/utils/sceneLoader'

interface Props {
  projectConfig?: ProjectConfig
  customConfig?: ViewerConfig
  modelUrl?: string
  cameraConfig?: CameraConfig
  animTracks?: any[]
  skyboxTextureName?: string // 新增：天空球纹理名称
}

const props = withDefaults(defineProps<Props>(), {
  projectConfig: undefined,
  customConfig: () => ({ useCustomControl: true, disableTransitionEffectOnLoad: false }),
  modelUrl: undefined,
  cameraConfig: undefined,
  animTracks: () => [],
})

/**
  resetCameraAndRestartTour方法
 **/
const resetCameraAndRestartTour = async () => {
  // 1. 重置相机
  resetCamera()
  stopTour()

  // 构造新的配置：合并 transform.json 中的相机配置（cameraConfig）
  const projectId = SceneLoader.getProjectIdFromUrl()

  if (projectId) {
    try {
      const sceneData = await SceneLoader.loadSceneData(projectId)
      const modelUrl = SceneLoader.getEffectiveModelUrl(sceneData)
      const cameraConfig = SceneLoader.getEffectiveCameraConfig(sceneData)

      const newConfig = {
        ...props.customConfig,
        ...props.projectConfig?.customConfig,
        ...cameraConfig,
      }

      // 重新初始化 viewer
      //await resetViewer(newConfig)

      if (modelUrl) {
        //await loadModel(modelUrl)

        // 如果有动画轨道就重新开始漫游
        if (sceneData.animTracks && sceneData.animTracks.length > 0) {
          startAutoTour()
        }
      }
    } catch (error) {
      console.error('[重置失败]', error)
    }
  }
}

// 模板引用
const viewerRef = ref<HTMLElement>()

// 合并配置
const mergedConfig = {
  ...props.customConfig,
  ...props.projectConfig?.customConfig,
  ...props.cameraConfig,
}

// 创建响应式的天空盒纹理名称
const skyboxTextureNameRef = computed(() => props.skyboxTextureName || 'default')

// 使用viewer composable，传入响应式的天空球纹理名称
const { isLoading, error, loadModel, resetCamera, resetViewer, startAutoTour, stopTour } =
  useViewer(mergedConfig, props.animTracks, skyboxTextureNameRef)

// 暴露updateCamera方法给父组件
defineExpose({
  updateCamera: (config: CameraConfig) => {
    // 通过resetViewer重新初始化包含新相机配置的viewer
    const newConfig = { ...mergedConfig, ...config }
    resetViewer(newConfig)
  },
})

// 重试加载
const retry = async () => {
  const targetModelUrl =
    props.modelUrl || getDecodedModelUrl() || props.projectConfig?.defaultModelUrl
  if (targetModelUrl) {
    await loadModel(targetModelUrl)
  }
}

// 初始化加载模型
const initModel = async () => {
  await nextTick() // 确保DOM已渲染

  // 优先使用直接传入的modelUrl，其次使用URL参数，最后使用项目配置
  const targetModelUrl =
    props.modelUrl || getDecodedModelUrl() || props.projectConfig?.defaultModelUrl

  if (targetModelUrl) {
    console.log(`[ViewerContainer] 开始加载模型:`, targetModelUrl)
    await loadModel(targetModelUrl)

    // 模型加载完成后，如果有动画轨道则自动开始漫游
    if (props.animTracks && props.animTracks.length > 0) {
      await nextTick() // 确保模型完全加载
      startAutoTour()
    }
  } else {
    console.warn(`[ViewerContainer] 未找到模型URL`)
  }
}

// 监听项目配置变化
watch(
  () => props.projectConfig,
  async (newConfig) => {
    if (newConfig) {
      console.log(`[${newConfig.name}] 项目配置已更新`, newConfig)
      await resetViewer(mergedConfig)
      await initModel()
    }
  },
  { deep: true },
)

// 天空球纹理变化现在由 useViewer 内部处理

// 组件挂载后初始化
onMounted(() => {
  initModel()

  // 添加用户交互监听，在用户操作时停止漫游
  const handleUserInteraction = () => {
    console.log('stopTour')
    stopTour()
  }

  // 监听键盘事件（全局）
  window.addEventListener('keydown', handleUserInteraction)

  // 监听viewer容器内的鼠标和触摸事件
  if (viewerRef.value) {
    viewerRef.value.addEventListener('mousedown', handleUserInteraction)
    viewerRef.value.addEventListener('wheel', handleUserInteraction)
    viewerRef.value.addEventListener('touchstart', handleUserInteraction)
    viewerRef.value.addEventListener('pointerdown', handleUserInteraction) // 支持触摸板
  }
})
</script>

<style scoped>
.viewer-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.viewer-canvas {
  width: 100%;
  height: 100%;
}

.control-buttons {
  position: absolute;
  top: 80px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 100;
}

.reset-camera-btn {
  width: 48px;
  height: 48px;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.reset-camera-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

.reset-camera-btn:active {
  transform: scale(0.95);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 20px;
  border: 4px solid #333;
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.error-message {
  text-align: center;
  color: white;
  padding: 20px;
}

.error-message h3 {
  margin: 0 0 10px 0;
  color: #ff6b6b;
}

.error-message p {
  margin: 0 0 20px 0;
  opacity: 0.8;
}

.retry-btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background: #0056b3;
}
</style>
