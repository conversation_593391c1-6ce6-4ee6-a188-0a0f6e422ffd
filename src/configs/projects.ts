import type { ProjectConfig } from '@/types/project'

/**
 * 项目配置列表
 */
export const projectConfigs: Record<string, ProjectConfig> = {
  'village-mini': {
    id: 'village-mini',
    name: 'Village Mini',
    description: 'Village Mini项目的3DGS展示页面',
    defaultModelUrl: 'https://reall3d.com/demo-models/hornedlizard.spx',
    customConfig: {
      backgroundColor: '#000000',
      showWatermark: false,
      // 相机初始配置
      position: [10, 5, 20],
      lookAt: [0, 0, 0],
      lookUp: [0, -1, 0],
      fov: 50,
    },
  },
  'unicity-mini': {
    id: 'unicity-mini',
    name: 'Unicity Mini',
    description: 'Unicity Mini项目的3DGS展示页面',
    defaultModelUrl: 'https://reall3d.com/demo-models/yz.meta.json',
    customConfig: {
      backgroundColor: '#1a1a1a',
      showWatermark: true,
      // 相机初始配置
      position: [0, 1.03226, -9.64367],
      lookAt: [0, 0, 0],
      lookUp: [0, -1, 0],
      fov: 50,
    },
  },
  'unicity-web': {
    id: 'unicity-web',
    name: 'Unicity Web',
    description: 'Unicity Web项目的3DGS展示页面',
    defaultModelUrl: 'https://reall3d.com/demo-models/jtstjg.spx',
    customConfig: {
      backgroundColor: '#2a2a2a',
      showWatermark: false,
      enableAutoRotate: true,
      // 相机初始配置
      position: [15, 8, 25],
      lookAt: [5, 0, 5],
      lookUp: [0, -1, 0],
      fov: 50,
    },
  },
}

/**
 * 获取项目配置
 * @param projectId 项目ID
 * @returns 项目配置
 */
export function getProjectConfig(projectId: string): ProjectConfig | null {
  return projectConfigs[projectId] || null
}

/**
 * 获取所有项目配置
 * @returns 所有项目配置
 */
export function getAllProjectConfigs(): ProjectConfig[] {
  return Object.values(projectConfigs)
}
