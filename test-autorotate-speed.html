<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动旋转速度测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info h2 {
            margin-top: 0;
            color: #4CAF50;
        }
        .viewers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .viewer-item {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 15px;
        }
        .viewer-item h3 {
            margin-top: 0;
            color: #4CAF50;
            text-align: center;
        }
        .viewer-frame {
            width: 100%;
            height: 300px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background: #000;
        }
        .speed-info {
            text-align: center;
            margin-top: 10px;
            font-size: 14px;
            color: #ccc;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .controls button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="info">
            <h2>🔄 自动旋转速度对比测试</h2>
            <p><strong>测试说明：</strong></p>
            <ul>
                <li>✅ 下面展示了不同自动旋转速度的效果</li>
                <li>⚡ 速度单位：度/秒（数值越大旋转越快）</li>
                <li>🎮 每个查看器都可以独立交互</li>
                <li>⏸️ 交互时旋转会暂停，停止交互3秒后恢复</li>
                <li>🔧 你可以在项目配置中设置 <code>autoRotateSpeed</code> 来调整速度</li>
            </ul>
        </div>
        
        <div class="viewers-grid">
            <div class="viewer-item">
                <h3>慢速旋转</h3>
                <iframe 
                    src="http://localhost:5173/?projectId=unicity-web&autoRotateSpeed=0.5" 
                    class="viewer-frame"
                    frameborder="0">
                </iframe>
                <div class="speed-info">速度: 0.5 度/秒</div>
            </div>
            
            <div class="viewer-item">
                <h3>正常速度</h3>
                <iframe 
                    src="http://localhost:5173/?projectId=unicity-web&autoRotateSpeed=1.5" 
                    class="viewer-frame"
                    frameborder="0">
                </iframe>
                <div class="speed-info">速度: 1.5 度/秒（项目默认）</div>
            </div>
            
            <div class="viewer-item">
                <h3>快速旋转</h3>
                <iframe 
                    src="http://localhost:5173/?projectId=unicity-web&autoRotateSpeed=3.0" 
                    class="viewer-frame"
                    frameborder="0">
                </iframe>
                <div class="speed-info">速度: 3.0 度/秒</div>
            </div>
            
            <div class="viewer-item">
                <h3>超快速度</h3>
                <iframe 
                    src="http://localhost:5173/?projectId=unicity-web&autoRotateSpeed=5.0" 
                    class="viewer-frame"
                    frameborder="0">
                </iframe>
                <div class="speed-info">速度: 5.0 度/秒</div>
            </div>
        </div>
        
        <div class="controls">
            <button onclick="refreshAll()">🔄 刷新所有查看器</button>
            <button onclick="openConfig()">⚙️ 查看配置文件</button>
        </div>
        
        <div class="info">
            <h3>💡 配置说明</h3>
            <p>在项目配置文件中设置自动旋转速度：</p>
            <pre style="background: #1a1a1a; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>// src/configs/projects.ts
customConfig: {
  autoRotate: true,
  autoRotateSpeed: 1.5,  // 调整这个值来改变旋转速度
  useCustomControl: false,
  // 其他配置...
}</code></pre>
        </div>
    </div>

    <script>
        function refreshAll() {
            const iframes = document.querySelectorAll('.viewer-frame');
            iframes.forEach(iframe => {
                iframe.src = iframe.src;
            });
        }
        
        function openConfig() {
            // 这里可以打开配置文件或配置页面
            alert('请查看 src/configs/projects.ts 文件来修改配置');
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('🔄 自动旋转速度测试页面已加载');
            console.log('📋 不同速度对比：');
            console.log('- 0.5 度/秒：慢速，适合细致观察');
            console.log('- 1.5 度/秒：正常速度，平衡观察和动态效果');
            console.log('- 3.0 度/秒：快速，突出动态效果');
            console.log('- 5.0 度/秒：超快，适合演示或吸引注意');
        });
    </script>
</body>
</html>
