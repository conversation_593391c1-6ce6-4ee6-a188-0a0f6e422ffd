<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动旋转测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .info {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info h2 {
            margin-top: 0;
            color: #4CAF50;
        }
        .info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .info li {
            margin: 5px 0;
        }
        .viewer-frame {
            width: 100%;
            height: 600px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background: #000;
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        .controls button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .controls button:hover {
            background: #45a049;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #2a2a2a;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="info">
            <h2>🔄 自动旋转功能测试</h2>
            <p><strong>测试说明：</strong></p>
            <ul>
                <li>✅ 页面加载后，模型应该会自动开始旋转</li>
                <li>⏸️ 当你与模型交互（鼠标拖拽、滚轮缩放等）时，自动旋转会暂停</li>
                <li>▶️ 停止交互3秒后，自动旋转会重新开始</li>
                <li>🎮 按 R 键可以手动切换自动旋转开关</li>
                <li>⚙️ 当前配置：useCustomControl: false, autoRotate: true</li>
            </ul>
        </div>
        
        <iframe
            src="http://localhost:5173/?projectId=unicity-web"
            class="viewer-frame"
            frameborder="0">
        </iframe>
        
        <div class="controls">
            <button onclick="refreshViewer()">🔄 刷新查看器</button>
            <button onclick="openInNewTab()">🔗 在新标签页打开</button>
        </div>
        
        <div class="status">
            <p>💡 <strong>提示：</strong>如果看不到自动旋转，请检查浏览器控制台是否有错误信息</p>
        </div>
    </div>

    <script>
        function refreshViewer() {
            const iframe = document.querySelector('.viewer-frame');
            iframe.src = iframe.src;
        }
        
        function openInNewTab() {
            window.open('http://localhost:5173/?projectId=unicity-web', '_blank');
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('🔄 自动旋转测试页面已加载');
            console.log('📋 测试步骤：');
            console.log('1. 等待模型加载完成');
            console.log('2. 观察模型是否自动旋转');
            console.log('3. 尝试拖拽模型，观察旋转是否暂停');
            console.log('4. 停止交互3秒，观察旋转是否恢复');
        });
    </script>
</body>
</html>
